[{"id": "test_01", "alarmName": "微信斗地主(H5)--测试监控告警(小时)-变化率(动态阈值)", "alarmType": "hour", "status": "1", "notifyTypes": ["dingding"], "appId": "h5_lord_cgid", "alarmConditionLinkList": [{"conditionId": "14", "valueType": "12", "geValue": 70, "minValue": 0, "maxValue": 20, "status": "1"}]}, {"id": "test_01", "alarmName": "美团斗地主(H5)--测试监控告警(小时)-变化率(动态阈值)", "alarmType": "hour", "status": "1", "notifyTypes": ["dingding"], "appId": "h5_mtlord_cgid", "alarmConditionLinkList": [{"conditionId": "14", "valueType": "12", "geValue": 70, "minValue": 0, "maxValue": 20, "status": "1"}]}, {"id": "test_01", "alarmName": "抖音斗地主(H5)--测试监控告警(小时)-变化率(动态阈值)", "alarmType": "hour", "status": "1", "notifyTypes": ["dingding"], "appId": "h5_douyin_cgid", "alarmConditionLinkList": [{"conditionId": "14", "valueType": "12", "geValue": 70, "minValue": 0, "maxValue": 20, "status": "1"}]}, {"id": "test_01", "alarmName": "美团麻将(H5)--测试监控告警(小时)-变化率(动态阈值)", "alarmType": "hour", "status": "1", "notifyTypes": ["dingding"], "appId": "h5_mtmah<PERSON>_cgid", "alarmConditionLinkList": [{"conditionId": "14", "valueType": "12", "geValue": 70, "minValue": 0, "maxValue": 20, "status": "1"}]}, {"id": "test_01", "alarmName": "微信象棋(H5)--测试监控告警(小时)-变化率(动态阈值)", "alarmType": "hour", "status": "1", "notifyTypes": ["dingding"], "appId": "h5_wxchess_cgid", "alarmConditionLinkList": [{"conditionId": "14", "valueType": "12", "geValue": 70, "minValue": 0, "maxValue": 20, "status": "1"}]}, {"id": "test_01", "alarmName": "UC小游戏斗地主(H5)--测试监控告警(小时)-变化率(动态阈值)", "alarmType": "hour", "status": "0", "notifyTypes": ["dingding"], "appId": "h5_uc<PERSON>_cgid", "alarmConditionLinkList": [{"conditionId": "14", "valueType": "12", "geValue": 70, "minValue": 0, "maxValue": 20, "status": "1"}]}, {"id": "test_01", "alarmName": "微信麻将(H5)--测试监控告警(小时)-变化率(动态阈值)", "alarmType": "hour", "status": "1", "notifyTypes": ["dingding"], "appId": "h5_mahjong_cgid", "alarmConditionLinkList": [{"conditionId": "14", "valueType": "12", "geValue": 70, "minValue": 0, "maxValue": 20, "status": "1"}]}, {"id": "test_01", "alarmName": "美团象棋(H5)--测试监控告警(小时)-变化率(动态阈值)", "alarmType": "hour", "status": "1", "notifyTypes": ["dingding"], "appId": "h5_mtchess_cgid", "alarmConditionLinkList": [{"conditionId": "14", "valueType": "12", "geValue": 70, "minValue": 0, "maxValue": 20, "status": "1"}]}, {"id": "test_01", "alarmName": "抖音麻将(H5)--测试监控告警(小时)-变化率(动态阈值)", "alarmType": "hour", "status": "1", "notifyTypes": ["dingding"], "appId": "h5_ttmah<PERSON>_cgid", "alarmConditionLinkList": [{"conditionId": "14", "valueType": "12", "geValue": 70, "minValue": 0, "maxValue": 20, "status": "1"}]}, {"id": "test_01", "alarmName": "微信元气枪神(H5)--测试监控告警(小时)-变化率(动态阈值)", "alarmType": "hour", "status": "1", "notifyTypes": ["dingding"], "appId": "h5_wxyuanqqs_cgid", "alarmConditionLinkList": [{"conditionId": "14", "valueType": "12", "geValue": 70, "minValue": 0, "maxValue": 20, "status": "1"}]}, {"id": "test_01", "alarmName": "抖音象棋(H5)--测试监控告警(小时)-变化率(动态阈值)", "alarmType": "hour", "status": "1", "notifyTypes": ["dingding"], "appId": "h5_ttchess_cgid", "alarmConditionLinkList": [{"conditionId": "14", "valueType": "12", "geValue": 70, "minValue": 0, "maxValue": 20, "status": "1"}]}, {"id": "test_01", "alarmName": "热血街篮(Android)--测试监控告警(小时)-变化率(动态阈值)", "alarmType": "hour", "status": "1", "notifyTypes": ["dingding"], "appId": "a3504ba50f", "alarmConditionLinkList": [{"conditionId": "15", "valueType": "12", "geValue": 70, "minValue": 0, "maxValue": 20, "status": "1"}, {"conditionId": "16", "valueType": "12", "geValue": 150, "minValue": 0, "maxValue": 20, "status": "1"}, {"conditionId": "17", "valueType": "12", "geValue": 70, "minValue": 0, "maxValue": 20, "status": "1"}]}, {"id": "test_01", "alarmName": "热血街篮(iOS)--测试监控告警(小时)-变化率(动态阈值)", "alarmType": "hour", "status": "1", "notifyTypes": ["dingding"], "appId": "89754a9c4f", "alarmConditionLinkList": [{"conditionId": "15", "valueType": "12", "geValue": 70, "minValue": 0, "maxValue": 20, "status": "1"}, {"conditionId": "16", "valueType": "12", "geValue": 70, "minValue": 0, "maxValue": 20, "status": "1"}, {"conditionId": "17", "valueType": "12", "geValue": 70, "minValue": 0, "maxValue": 20, "status": "1"}]}, {"id": "test_01", "alarmName": "京东麻将(H5)--测试监控告警(小时)-变化率(动态阈值)", "alarmType": "hour", "status": "1", "notifyTypes": ["dingding"], "appId": "h5_jd<PERSON><PERSON>_cgid", "alarmConditionLinkList": [{"conditionId": "14", "valueType": "12", "geValue": 70, "minValue": 0, "maxValue": 20, "status": "1"}]}, {"id": "test_01", "alarmName": "微信掼蛋(H5)--测试监控告警(小时)-变化率(动态阈值)", "alarmType": "hour", "status": "1", "notifyTypes": ["dingding"], "appId": "h5_wxguandan_cgid", "alarmConditionLinkList": [{"conditionId": "14", "valueType": "12", "geValue": 70, "minValue": 0, "maxValue": 20, "status": "1"}]}]