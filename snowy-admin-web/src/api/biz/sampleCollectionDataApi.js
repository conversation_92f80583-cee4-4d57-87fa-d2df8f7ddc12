import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/sampleCollection/data/` + url, ...arg)

/**
 * 样本采集数据表Api接口管理器
 *
 * <AUTHOR>
 * @date  2023/03/23 11:43
 **/
export default {
	// 获取样本采集数据表分页
	sampleCollectionDataPage(data) {
		return request('page', data, 'get')
	},
	// 获取样本采集数据表列表
	sampleCollectionDataList(data) {
		return request('list', data, 'get')
	},
	// 提交样本采集数据表表单 edit为true时为编辑，默认为新增
	sampleCollectionDataSubmitForm(data, edit = false) {
		return request(edit ? 'add' : 'edit', data)
	},
	// 删除样本采集数据表
	sampleCollectionDataDelete(data) {
		return request('delete', data)
	},
	// 获取样本采集数据表详情
	sampleCollectionDataDetail(data) {
		return request('detail', data, 'get')
	},
	// 清空已删除样本采集数据
	sampleCollectionDataClear(data) {
		return request('clear', data)
	}
}
