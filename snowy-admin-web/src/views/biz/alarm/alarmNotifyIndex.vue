<template>
    <a-card :bordered="false">
        <a-form ref="searchFormRef" name="advanced_search" :model="searchFormState" class="ant-advanced-search-form mb-4">
            <a-row :gutter="24">
            <!--
                <a-col :span="6">
                    <a-form-item label="通知名称" name="notifyName">
                        <a-input v-model:value="searchFormState.notifyName" placeholder="请输入通知名称" />
                    </a-form-item>
                </a-col>
                <a-col :span="6">
                    <a-form-item label="告警周期" name="alarmType">
                        <a-select v-model:value="searchFormState.alarmType" placeholder="请选择告警周期" :options="alarmTypeOptions" />
                    </a-form-item>
                </a-col>
                -->
                <a-col :span="6">
                    <a-form-item label="游戏ID" name="gameId">
                        <a-input v-model:value="searchFormState.gameId" placeholder="请选择游戏ID" />
                    </a-form-item>
                </a-col>
                <a-col :span="6">
                    <a-form-item label="通知信息分组" name="groupId">
                        <a-select v-model:value="searchFormState.groupId" placeholder="请选择通知信息分组" optionFilterProp="groupName" :options="alarmNotifyGroupOptions" :field-names="{ label: 'groupName', value: 'id' }" show-search allow-clear/>
                    </a-form-item>
                </a-col>
                <template v-if="advanced">
                    <a-col :span="6">
                        <a-form-item label="比赛产品ID" name="mpId">
                            <a-input v-model:value="searchFormState.mpId" placeholder="请选择比赛产品ID" />
                        </a-form-item>
                    </a-col>
                </template>
            <!--
                <template v-if="advanced">
                    <a-col :span="6">
                        <a-form-item label="群机器人消息token" name="robotToken">
                            <a-input v-model:value="searchFormState.robotToken" placeholder="请输入群机器人消息token" />
                        </a-form-item>
                    </a-col>
                </template>
                -->
                <template v-if="advanced">
                    <a-col :span="6">
                        <a-form-item label="描述" name="description">
                            <a-input v-model:value="searchFormState.description" placeholder="请输入描述" />
                        </a-form-item>
                    </a-col>
                </template>
                <template v-if="advanced">
                    <a-col :span="6">
                        <a-form-item label="状态" name="status">
                            <a-select v-model:value="searchFormState.status" placeholder="请选择状态" :options="statusOptions" />
                        </a-form-item>
                    </a-col>
                </template>
                <a-col :span="6">
                    <a-button type="primary" @click="table.refresh(true)">查询</a-button>
                    <a-button style="margin: 0 8px" @click="() => searchFormRef.resetFields()">重置</a-button>
                    <a @click="toggleAdvanced" style="margin-left: 8px">
                        {{ advanced ? '收起' : '展开' }}
                        <component :is="advanced ? 'up-outlined' : 'down-outlined'"/>
                    </a>
                </a-col>
            </a-row>
        </a-form>
        <s-table
            ref="table"
            :columns="columns"
            :data="loadData"
            :alert="options.alert.show"
            bordered
            :row-key="(record) => record.id"
            :tool-config="toolConfig"
            :row-selection="options.rowSelection">
            <template #operator class="table-operator">
                <a-space>
                    <a-button type="primary" @click="formRef.onOpen()" v-if="hasPerm('alarmNotifyAdd')">
                        <template #icon><plus-outlined /></template>
                        新增
                    </a-button>
                    <a-button danger @click="deleteBatchAlarmNotify()" v-if="hasPerm('alarmNotifyBatchDelete')">删除</a-button>
                </a-space>
            </template>
            <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'alarmType'">
                    {{ $TOOL.dictTypeData('mps_monitor_alarm_type', record.alarmType) }}
                </template>
                <template v-if="column.dataIndex === 'notifyType'">
                  <div v-if="record.notifyType && (JSON.parse(record.notifyType)).length > 0">
                    <a-tag v-for="textValue in JSON.parse(record.notifyType)" :key="textValue" color="green">{{ $TOOL.dictTypeData('mps_monitor_notify_type', textValue) }}</a-tag>
                  </div>
                  <div v-else></div>
                </template>
                <template v-if="column.dataIndex === 'receiveUser'">
                  <div v-if="record.receiveUser && (JSON.parse(record.receiveUser)).length > 0">
                    <a-tag v-for="textValue in JSON.parse(record.receiveUser)" :key="textValue" color="blue" style="margin-bottom: 4px">{{ textValue }}</a-tag>
                  </div>
                  <div v-else></div>
                </template>
                <template v-if="column.dataIndex === 'ccUser'">
                  <div v-if="record.ccUser && (JSON.parse(record.ccUser)).length > 0">
                    <a-tag v-for="textValue in JSON.parse(record.ccUser)" :key="textValue" color="blue" style="margin-bottom: 4px">{{ textValue }}</a-tag>
                  </div>
                  <div v-else></div>
                </template>
                <template v-if="column.dataIndex === 'status'">
                	<a-switch
                		:loading="statusLoading"
                		:checked="record.status === '1'"
                		@change="editStatus(record)"
                		checkedChildren="开启"
                		unCheckedChildren="关闭"
                		/>
                </template>
                <template v-if="column.dataIndex === 'action'">
                    <a-space>
                        <a @click="formRef.onOpen(record)" v-if="hasPerm('alarmNotifyEdit')">编辑</a>
                        <a-divider type="vertical" v-if="hasPerm(['alarmNotifyEdit', 'alarmNotifyDelete'], 'and')" />
                        <a-popconfirm title="确定要删除吗？" @confirm="deleteAlarmNotify(record)">
                            <a-button type="link" danger size="small" v-if="hasPerm('alarmNotifyDelete')">删除</a-button>
                        </a-popconfirm>
                    </a-space>
                </template>
            </template>
        </s-table>
    </a-card>
    <Form ref="formRef" @successful="table.refresh(true)" />
</template>

<script setup name="alarmNotifyIndex">
    import { message } from 'ant-design-vue'
    import tool from '@/utils/tool'
	import { useRoute } from 'vue-router';
    import Form from './alarmNotifyForm.vue'
    import alarmNotifyApi from '@/api/biz/alarmNotifyApi'
    import alarmNotifyGroupApi from '@/api/biz/alarmNotifyGroupApi'
    let searchFormState = reactive({})
    const searchFormRef = ref()
    const table = ref()
    const formRef = ref()
    const toolConfig = { refresh: true, height: true, columnSetting: true, striped: false }
    const statusLoading = ref(false)
    // 查询区域显示更多控制
    const advanced = ref(false)
    const toggleAdvanced = () => {
        advanced.value = !advanced.value
    }
    const columns = [
//        {
//            title: '通知名称',
//            dataIndex: 'notifyName'
//        },
//        {
//            title: '告警周期',
//            dataIndex: 'alarmType'
//        },
//        {
//            title: '接收方式',
//            dataIndex: 'notifyType'
//        },
//        {
//            title: '游戏ID',
//            dataIndex: 'gameId'
//        },
        {
            title: '游戏名称',
            dataIndex: 'gameNameZh'
        },
        {
            title: '通知分组名',
            dataIndex: 'groupName'
        },
        {
            title: '比赛产品ID',
            dataIndex: 'mpId'
        },
        {
            title: '接收人',
            dataIndex: 'receiveUserNames'
        },
        {
            title: '抄送人',
            dataIndex: 'ccUserNames'
        },
        {
            title: '群机器人消息token',
            dataIndex: 'robotToken',
            ellipsis: true
        },
        {
            title: '描述',
            dataIndex: 'description',
            ellipsis: true
        },
        {
            title: '创建人',
            dataIndex: 'createUserName'
        },
        {
            title: '更新人',
            dataIndex: 'updateUserName'
        },
        {
            title: '创建时间',
            dataIndex: 'createTime'
        },
        {
            title: '状态',
            dataIndex: 'status'
        },
    ]
    // 操作栏通过权限判断是否显示
    if (hasPerm(['alarmNotifyEdit', 'alarmNotifyDelete'])) {
        columns.push({
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: '150px'
        })
    }
    let selectedRowKeys = ref([])
    // 列表选择配置
    const options = {
        alert: {
            show: false,
            clear: () => {
                selectedRowKeys = ref([])
            }
        },
        rowSelection: {
            onChange: (selectedRowKey, selectedRows) => {
                selectedRowKeys.value = selectedRowKey
            }
        }
    }
    const loadData = (parameter) => {
        loadAlarmNotifyGroupData();
        const searchFormParam = JSON.parse(JSON.stringify(searchFormState))
        return alarmNotifyApi.alarmNotifyPage(Object.assign(parameter, searchFormParam)).then((data) => {
            return data
        })
    }
    	// 修改状态
    	const editStatus = (record) => {
    		statusLoading.value = true
        	let params = {
                	id: record.id,
                	status: record.status === '1' ? '0' : '1'
            	}
    		alarmNotifyApi.alarmNotifyEditStatus(params)
    			.then(() => {
    				table.value.refresh()
    			}).finally(() => {
    				statusLoading.value = false
    			})
    	}
    // 删除
    const deleteAlarmNotify = (record) => {
        let params = [
            {
                id: record.id
            }
        ]
        alarmNotifyApi.alarmNotifyDelete(params).then(() => {
            table.value.refresh(true)
        })
    }
    // 批量删除
    const deleteBatchAlarmNotify = () => {
        if (selectedRowKeys.value.length < 1) {
            message.warning('请选择一条或多条数据')
            return false
        }
        const params = selectedRowKeys.value.map((m) => {
            return {
                id: m
            }
        })
        alarmNotifyApi.alarmNotifyDelete(params).then(() => {
            table.value.clearRefreshSelected()
        })
    }
    const alarmTypeOptions = tool.dictList('mps_monitor_alarm_type')
    const statusOptions = tool.dictList('status')
    const alarmNotifyGroupOptions = ref([])

    // 获取通知信息分组列表
    const loadAlarmNotifyGroupData = (parameter) => {
        alarmNotifyGroupApi.alarmNotifyGroupList(parameter).then((data) => {
            alarmNotifyGroupOptions.value = data
        })
    }

    // 从路由中获取参数并弹出页面
	const Route = useRoute();
    onMounted(() => {
      // 在组件创建时从路由查询参数中获取 gameId和mpId
      const gameId = Route.query.gameId;
      const mpId = Route.query.mpId;
      let recordParam = {};

      if (gameId) {
        // 确保 gameId 是有效的数字再添加
        const gameIdNumber = Number(gameId);
        if (!isNaN(gameIdNumber)) {
          recordParam.gameId = gameIdNumber;
        } else {
          console.warn('gameId 无法转换为有效的数字');
        }
      }
      if (mpId) {
        // 确保 mpId 是有效的数字再添加
        const mpIdNumber = Number(mpId);
        if (!isNaN(mpIdNumber)) {
          recordParam.mpId = mpIdNumber;
        } else {
          console.warn('mpId 无法转换为有效的数字');
        }
      }

       // gameId 或 mpId 存在时，弹出并传入参数
       if (recordParam.gameId || recordParam.mpId) {
			// 默认选中安全组人员
			recordParam.ccUsers = ['7572', '5006', '8325', '10300', '4369', '98', '5213'];
			formRef.value.onOpen(recordParam);
       }

    })
</script>
