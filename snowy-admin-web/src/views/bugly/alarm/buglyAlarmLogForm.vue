<template>
    <a-drawer
        :title="formData.id ? '编辑告警日志记录表' : '增加告警日志记录表'"
        :width="600"
        :visible="visible"
        :destroy-on-close="true"
        :footer-style="{ textAlign: 'right' }"
        @close="onClose"
    >
        <a-form ref="formRef" :model="formData" :rules="formRules" layout="vertical">
            <a-form-item label="日期：" name="day">
                <a-input v-model:value="formData.day" placeholder="请输入日期" allow-clear />
            </a-form-item>
            <a-form-item label="小时：" name="hour">
                <a-input v-model:value="formData.hour" placeholder="请输入小时" allow-clear />
            </a-form-item>
            <a-form-item label="告警信息ID：" name="alarmId">
                <a-select v-model:value="formData.alarmId" placeholder="请选择告警信息ID" :options="alarmIdOptions" />
            </a-form-item>
            <a-form-item label="统计周期：" name="alarmType">
                <a-select v-model:value="formData.alarmType" placeholder="请选择统计周期" :options="alarmTypeOptions" />
            </a-form-item>
            <a-form-item label="接收方式：" name="notifyType">
                <a-input v-model:value="formData.notifyType" placeholder="请输入接收方式" allow-clear />
            </a-form-item>
            <a-form-item label="统计数据表ID：" name="dataId">
                <a-input v-model:value="formData.dataId" placeholder="请输入统计数据表ID" allow-clear />
            </a-form-item>
            <a-form-item label="统计数据类型：" name="dataType">
                <a-input v-model:value="formData.dataType" placeholder="请输入统计数据类型" allow-clear />
            </a-form-item>
            <a-form-item label="应用ID：" name="appId">
                <a-select v-model:value="formData.appId" placeholder="请选择应用ID" :options="appIdOptions" />
            </a-form-item>
            <a-form-item label="应用名称：" name="appName">
                <a-input v-model:value="formData.appName" placeholder="请输入应用名称" allow-clear />
            </a-form-item>
            <a-form-item label="应用类型：" name="appPlatform">
                <a-input v-model:value="formData.appPlatform" placeholder="请输入应用类型" allow-clear />
            </a-form-item>
            <a-form-item label="APP版本号(该字段非空则告警作用于特定版本)：" name="appVersion">
                <a-input v-model:value="formData.appVersion" placeholder="请输入APP版本号(该字段非空则告警作用于特定版本)" allow-clear />
            </a-form-item>
            <a-form-item label="APP渠道号(该字段非空则告警作用于特定渠道)：" name="appChannel">
                <a-input v-model:value="formData.appChannel" placeholder="请输入APP渠道号(该字段非空则告警作用于特定渠道)" allow-clear />
            </a-form-item>
            <a-form-item label="接收人ID,JSONString：" name="receiveUser">
                <a-input v-model:value="formData.receiveUser" placeholder="请输入接收人ID,JSONString" allow-clear />
            </a-form-item>
            <a-form-item label="抄送人ID,JSONString：" name="ccUser">
                <a-input v-model:value="formData.ccUser" placeholder="请输入抄送人ID,JSONString" allow-clear />
            </a-form-item>
            <a-form-item label="告警结果数据,JSONString：" name="templateData">
                <a-input v-model:value="formData.templateData" placeholder="请输入告警结果数据,JSONString" allow-clear />
            </a-form-item>
            <a-form-item label="告警消息内容,JSONString：" name="alarmMessages">
                <a-input v-model:value="formData.alarmMessages" placeholder="请输入告警消息内容,JSONString" allow-clear />
            </a-form-item>
            <a-form-item label="触发的告警条件,JSONString：" name="alarmConditions">
                <a-input v-model:value="formData.alarmConditions" placeholder="请输入触发的告警条件,JSONString" allow-clear />
            </a-form-item>
            <a-form-item label="描述：" name="description">
                <a-input v-model:value="formData.description" placeholder="请输入描述" allow-clear />
            </a-form-item>
            <a-form-item label="日期(小时)的datetime类型：" name="dt">
                <a-date-picker v-model:value="formData.dt" value-format="YYYY-MM-DD HH:mm:ss" show-time placeholder="请选择日期(小时)的datetime类型" style="width: 100%" />
            </a-form-item>
            <a-form-item label="状态，=0发送失败，=1发送成功，=2不发送该条告警，=-1不发送该条告警(数据异常)：" name="status">
                <a-select v-model:value="formData.status" placeholder="请选择状态，=0发送失败，=1发送成功，=2不发送该条告警，=-1不发送该条告警(数据异常)" :options="statusOptions" />
            </a-form-item>
        </a-form>
        <template #footer>
            <a-button style="margin-right: 8px" @click="onClose">关闭</a-button>
            <a-button type="primary" @click="onSubmit" :loading="submitLoading">保存</a-button>
        </template>
    </a-drawer>
</template>

<script setup name="buglyAlarmLogForm">
    import tool from '@/utils/tool'
    import { cloneDeep } from 'lodash-es'
    import { required } from '@/utils/formRules'
    import buglyAlarmLogApi from '@/api/bugly/buglyAlarmLogApi'
    // 抽屉状态
    const visible = ref(false)
    const emit = defineEmits({ successful: null })
    const formRef = ref()
    // 表单数据
    const formData = ref({})
    const submitLoading = ref(false)
    const alarmIdOptions = ref([])
    const alarmTypeOptions = ref([])
    const appIdOptions = ref([])
    const statusOptions = ref([])

    // 打开抽屉
    const onOpen = (record) => {
        visible.value = true
        if (record) {
            let recordData = cloneDeep(record)
            formData.value = Object.assign({}, recordData)
        }
        alarmIdOptions.value = tool.dictList('status')
        alarmTypeOptions.value = tool.dictList('status')
        appIdOptions.value = tool.dictList('status')
        statusOptions.value = tool.dictList('status')
    }
    // 关闭抽屉
    const onClose = () => {
        formRef.value.resetFields()
        formData.value = {}
        visible.value = false
    }
    // 默认要校验的
    const formRules = {
        day: [required('请输入日期')],
        hour: [required('请输入小时')],
        alarmId: [required('请输入告警信息ID')],
        alarmType: [required('请输入统计周期')],
        notifyType: [required('请输入接收方式')],
        dataId: [required('请输入统计数据表ID')],
        dataType: [required('请输入统计数据类型')],
        appId: [required('请输入应用ID')],
        receiveUser: [required('请输入接收人ID,JSONString')],
        status: [required('请输入状态，=0发送失败，=1发送成功，=2不发送该条告警，=-1不发送该条告警(数据异常)')],
    }
    // 验证并提交数据
    const onSubmit = () => {
        formRef.value
            .validate()
            .then(() => {
                submitLoading.value = true
                const formDataParam = cloneDeep(formData.value)
                buglyAlarmLogApi
                    .buglyAlarmLogSubmitForm(formDataParam, !formDataParam.id)
                    .then(() => {
                        onClose()
                        emit('successful')
                    })
                    .finally(() => {
                        submitLoading.value = false
                    })
            })
    }
    // 抛出函数
    defineExpose({
        onOpen
    })
</script>
