<template>
    <a-card :bordered="false">
        <a-form ref="searchFormRef" name="advanced_search" :model="searchFormState" class="ant-advanced-search-form mb-4">
            <a-row :gutter="24">
                <a-col :span="4">
                    <a-form-item label="平台" name="sourceType">
                        <a-select v-model:value="searchFormState.sourceType" placeholder="请选择平台" :options="sourceTypeOptions" />
                    </a-form-item>
                </a-col>
                <a-col :span="4">
                    <a-form-item label="关键字类型" name="kwType">
                        <a-select v-model:value="searchFormState.kwType" placeholder="请选择关键字类型" :options="kwTypeOptions" />
                    </a-form-item>
                </a-col>
                <a-col :span="5">
                    <a-form-item label="关键词" name="kw">
                        <a-input v-model:value="searchFormState.kw" placeholder="请输入关键词" />
                    </a-form-item>
                </a-col>
                    <a-col :span="4">
                        <a-form-item label="备注" name="remark">
                            <a-input v-model:value="searchFormState.remark" placeholder="请输入备注" />
                        </a-form-item>
                    </a-col>
                    <a-col :span="3">
                        <a-form-item label="状态" name="status">
                            <a-select v-model:value="searchFormState.status" placeholder="请选择状态" :options="statusOptions" />
                        </a-form-item>
                    </a-col>
                <a-col :span="4">
                    <a-button type="primary" @click="table.refresh(true)">查询</a-button>
                    <a-button style="margin: 0 8px" @click="() => searchFormRef.resetFields()">重置</a-button>
                </a-col>
            </a-row>
        </a-form>
        <s-table
            ref="table"
            :columns="columns"
            :data="loadData"
            :alert="options.alert.show"
            bordered
            :row-key="(record) => record.id"
            :tool-config="toolConfig"
            :row-selection="options.rowSelection">
            <template #operator class="table-operator">
                <a-space>
                    <a-button type="primary" @click="formRef.onOpen()" v-if="hasPerm('enemyKwAdd')">
                        <template #icon><plus-outlined /></template>
                        新增
                    </a-button>
                    <a-button danger @click="deleteBatchEnemyKw()" v-if="hasPerm('enemyKwBatchDelete')">删除</a-button>
                </a-space>
            </template>
            <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'sourceType'">
					<a-tag v-if="record.sourceType === 'tieba'" color="#4aaaee">
						{{ $TOOL.dictTypeData('enemy_msg_source_type', record.sourceType) }}
					</a-tag>
					<a-tag v-else-if="record.sourceType === 'goofish'" color="#ffe60f">
						{{ $TOOL.dictTypeData('enemy_msg_source_type', record.sourceType) }}
					</a-tag>
					<a-tag v-else-if="record.sourceType === 'wechat'" color="#2aae67">
						{{ $TOOL.dictTypeData('enemy_msg_source_type', record.sourceType) }}
					</a-tag>
					<a-tag v-else-if="record.sourceType === 'taobao'" color="#ff6203">
						{{ $TOOL.dictTypeData('enemy_msg_source_type', record.sourceType) }}
					</a-tag>
					<a-tag v-else >
						{{ record.sourceType }}
					</a-tag>
                </template>
                <template v-if="column.dataIndex === 'kwType'">
					<a-tag v-if="record.kwType === 'tieba_hit'" color="blue">
						{{ $TOOL.dictTypeData('enemy_msg_kw_type', record.kwType) }}
					</a-tag>
					<a-tag v-else-if="record.kwType === 'goofish_search'" color="orange">
						{{ $TOOL.dictTypeData('enemy_msg_kw_type', record.kwType) }}
					</a-tag>
					<a-tag v-else-if="record.kwType === 'tieba_name'" color="green">
						{{ $TOOL.dictTypeData('enemy_msg_kw_type', record.kwType) }}
					</a-tag>
					<a-tag v-else >
						{{ record.kwType }}
					</a-tag>
                </template>
                <template v-if="column.dataIndex === 'status'">
					<a-tag v-if="record.status === '1'" color="#87d068">
						{{ $TOOL.dictTypeData('status', record.status) }}
					</a-tag>
					<a-tag v-if="record.status === '0'" color="#f50">
						{{ $TOOL.dictTypeData('status', record.status) }}
					</a-tag>
					<a-tag v-if="record.status === '-1'" color="#108ee9">
						{{ $TOOL.dictTypeData('status', record.status) }}
					</a-tag>
                </template>
                <template v-if="column.dataIndex === 'action'">
                    <a-space>
                        <a @click="formRef.onOpen(record)" v-if="hasPerm('enemyKwEdit')">编辑</a>
                        <a-divider type="vertical" v-if="hasPerm(['enemyKwEdit', 'enemyKwDelete'], 'and')" />
                        <a-popconfirm title="确定要删除吗？" @confirm="deleteEnemyKw(record)">
                            <a-button type="link" danger size="small" v-if="hasPerm('enemyKwDelete')">删除</a-button>
                        </a-popconfirm>
                    </a-space>
                </template>
            </template>
        </s-table>
    </a-card>
    <Form ref="formRef" @successful="table.refresh(true)" />
</template>

<script setup name="enemyKwIndex">
    import { message } from 'ant-design-vue'
    import tool from '@/utils/tool'
    import Form from './enemyKwForm.vue'
    import enemyKwApi from '@/api/enemy/enemyKwApi'
    let searchFormState = reactive({})
    const searchFormRef = ref()
    const table = ref()
    const formRef = ref()
    const toolConfig = { refresh: true, height: true, columnSetting: true, striped: false }
    // 查询区域显示更多控制
    const advanced = ref(false)
    const toggleAdvanced = () => {
        advanced.value = !advanced.value
    }
    const columns = [
        {
            title: '平台',
            dataIndex: 'sourceType'
        },
        {
            title: '关键字类型',
            dataIndex: 'kwType'
        },
        {
            title: '关键词',
            dataIndex: 'kw'
        },
        {
            title: '备注',
            dataIndex: 'remark'
        },
        {
            title: '创建人',
            dataIndex: 'createUserName'
        },
        {
            title: '更新人',
            dataIndex: 'updateUserName'
        },
        {
            title: '创建时间',
            dataIndex: 'createTime'
        },
        {
            title: '最后更新时间',
            dataIndex: 'updateTime'
        },
        {
            title: '状态',
            dataIndex: 'status'
        },
    ]
    // 操作栏通过权限判断是否显示
    if (hasPerm(['enemyKwEdit', 'enemyKwDelete'])) {
        columns.push({
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: '150px'
        })
    }
    let selectedRowKeys = ref([])
    // 列表选择配置
    const options = {
        alert: {
            show: false,
            clear: () => {
                selectedRowKeys = ref([])
            }
        },
        rowSelection: {
            onChange: (selectedRowKey, selectedRows) => {
                selectedRowKeys.value = selectedRowKey
            }
        }
    }
    const loadData = (parameter) => {
        const searchFormParam = JSON.parse(JSON.stringify(searchFormState))
        return enemyKwApi.enemyKwPage(Object.assign(parameter, searchFormParam)).then((data) => {
            return data
        })
    }
    // 删除
    const deleteEnemyKw = (record) => {
        let params = [
            {
                id: record.id
            }
        ]
        enemyKwApi.enemyKwDelete(params).then(() => {
            table.value.refresh(true)
        })
    }
    // 批量删除
    const deleteBatchEnemyKw = () => {
        if (selectedRowKeys.value.length < 1) {
            message.warning('请选择一条或多条数据')
            return false
        }
        const params = selectedRowKeys.value.map((m) => {
            return {
                id: m
            }
        })
        enemyKwApi.enemyKwDelete(params).then(() => {
            table.value.clearRefreshSelected()
        })
    }
    const kwTypeOptions = tool.dictList('enemy_msg_kw_type')
    const sourceTypeOptions = tool.dictList('enemy_msg_source_type')
    const statusOptions = tool.dictList('status')
</script>
