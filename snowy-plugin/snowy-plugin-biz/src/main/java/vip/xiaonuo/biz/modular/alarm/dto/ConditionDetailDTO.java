package vip.xiaonuo.biz.modular.alarm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 告警基本信息关联的告警条件详情传输对象
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/3/10 14:31
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "告警基本信息关联的告警条件详情传输对象")
public class ConditionDetailDTO implements Serializable {

    private static final Long serialVersionUID = 1L;

    // 关联表
    /**
     * 告警基本信息ID
     */
    @ApiModelProperty(value = "告警基本信息ID", name = "alarmId", required = true)
    private String alarmId;
    /**
     * 告警条件ID
     */
    @ApiModelProperty(value = "告警条件ID", name = "conditionId", required = true)
    private String conditionId;
    /**
     * 大于等于>=
     */
    @ApiModelProperty(value = "大于等于>=", name = "geValue")
    private BigDecimal geValue;
    /**
     * 小于等于<=
     */
    @ApiModelProperty(value = "小于等于<=", name = "leValue")
    private BigDecimal leValue;
    /**
     * 基线，最小值，低于该值触发告警
     */
    @ApiModelProperty(value = "基线，最小值，低于该值触发告警", name = "minValue")
    private BigDecimal minValue;
    /**
     * 基线，最大值，超过该值触发告警
     */
    @ApiModelProperty(value = "基线，最大值，超过该值触发告警", name = "maxValue")
    private BigDecimal maxValue;
    /**
     * 条件值类型(=1自定义值,=2最近7日平均值,=3最近7日去掉最高和最低平均值,=4相对前一天变化率,=5相对最近7日平均值变化率,=6相对最近7日去掉最高和最低平均值变化率)
     */
    @ApiModelProperty(value = "条件值类型(=1自定义值,=2最近7日平均值,=3最近7日去掉最高和最低平均值,=4相对前一天变化率,=5相对最近7日平均值变化率,=6相对最近7日去掉最高和最低平均值变化率)", name = "valueType", required = true)
    private String valueType;

    /**
     * 扩展信息
     */
    @ApiModelProperty(value = "扩展信息", position = 8)
    private String extJson;
    /**
     * 关联状态，-1删除，0无效，1有效
     */
    @ApiModelProperty(value = "关联状态，-1删除，0无效，1有效", name = "status", required = true)
    private String alarmConditionLinkStatus;

    // 告警条件表
    /**
     * 条件名称
     */
    @ApiModelProperty(value = "条件名称", position = 2)
    private String conditionName;

    /**
     * 条件内容
     */
    @ApiModelProperty(value = "条件内容", position = 3)
    private String conditionContent;

    /**
     * 条件类型
     */
    @ApiModelProperty(value = "条件类型", position = 4)
    private String conditionType;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述", name = "description")
    private String description;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", name = "createUser")
    private String createUser;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", name = "createTime", required = true)
    private Date createTime;
    /**
     * 最后更新人
     */
    @ApiModelProperty(value = "最后更新人", name = "updateUser")
    private String updateUser;
    /**
     * 最后更新时间
     */
    @ApiModelProperty(value = "最后更新时间", name = "updateTime", required = true)
    private Date updateTime;
    /**
     * 条件状态，-1删除，0无效，1有效
     */
    @ApiModelProperty(value = "条件状态，-1删除，0无效，1有效", name = "status", required = true)
    private String conditionStatus;
}
