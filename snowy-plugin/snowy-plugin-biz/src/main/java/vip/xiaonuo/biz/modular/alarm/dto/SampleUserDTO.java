package vip.xiaonuo.biz.modular.alarm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * Description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/4/26 15:02
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "样本用户数据传输对象")
public class SampleUserDTO implements Serializable {

    private static final Long serialVersionUID = 1L;

    /**
     * 条件名称
     */
    @ApiModelProperty(value = "条件名称", name = "conditionName", required = true)
    private String conditionName;

    /**
     * 触发的告警条件详情
     */
    @ApiModelProperty(value = "触发的告警条件详情", name = "conditionDetailDTO")
    private ConditionDetailDTO conditionDetailDTO;
    /**
     * 样本用户ID
     */
    @ApiModelProperty(value = "样本用户ID", name = "userIds", required = true)
    private String userIds;

    @ApiModelProperty(value = "用户ID及标签", name = "userIdAndTagsMap", required = true)
    private Map<String, List<String>> userIdAndTagsMap;
}
