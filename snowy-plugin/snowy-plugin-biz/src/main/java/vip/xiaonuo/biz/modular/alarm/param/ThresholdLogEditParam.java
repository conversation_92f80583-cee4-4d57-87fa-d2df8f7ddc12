/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.alarm.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 监控阈值及触发结果日志记录编辑参数
 *
 * <AUTHOR>
 * @date  2023/03/31 11:18
 **/
@Getter
@Setter
public class ThresholdLogEditParam {

    /** 监控阈值日志记录ID */
    @ApiModelProperty(value = "监控阈值日志记录ID", required = true, position = 1)
    @NotBlank(message = "id不能为空")
    private String id;

    /** 日期 */
    @ApiModelProperty(value = "日期", position = 2)
    private String day;

    /** 小时 */
    @ApiModelProperty(value = "小时", position = 3)
    private Integer hour;

    /** 告警信息ID */
    @ApiModelProperty(value = "告警信息ID", position = 4)
    private String alarmId;

    /** 统计周期 */
    @ApiModelProperty(value = "统计周期", position = 5)
    private String alarmType;

    /** 比赛产品ID */
    @ApiModelProperty(value = "比赛产品ID", position = 6)
    private Long mpId;

    /** 统计数据表ID */
    @ApiModelProperty(value = "统计数据表ID", position = 7)
    private String dataId;

    /** 告警条件ID */
    @ApiModelProperty(value = "告警条件ID", position = 8)
    private String conditionId;

    /** 监控的值相对值(条件值类型为绝对值则该字段为绝对值，若条件值类型为变化率则为相对值) */
    @ApiModelProperty(value = "监控的值相对值(条件值类型为绝对值则该字段为绝对值，若条件值类型为变化率则为相对值)", position = 9)
    private BigDecimal monitorValueRelative;

    /** 监控的值绝对值 */
    @ApiModelProperty(value = "监控的值绝对值", position = 10)
    private BigDecimal monitorValueAbsolute;

    /** 相对的旧值绝对值，如最近7日平均值、前一天绝对值等) */
    @ApiModelProperty(value = "相对的旧值绝对值，如最近7日平均值、前一天绝对值等)", position = 11)
    private BigDecimal monitorValueOldAbsolute;

    /** 阈值绝对值(大于等于>=) */
    @ApiModelProperty(value = "阈值绝对值(大于等于>=)", position = 12)
    private BigDecimal geThresholdValueAbsolute;

    /** 阈值绝对值(小于等于<=) */
    @ApiModelProperty(value = "阈值绝对值(小于等于<=)", position = 13)
    private BigDecimal leThresholdValueAbsolute;

    /** 条件值，大于等于>= */
    @ApiModelProperty(value = "条件值，大于等于>=", position = 14)
    private BigDecimal geValue;

    /** 条件值，小于等于<= */
    @ApiModelProperty(value = "条件值，小于等于<=", position = 15)
    private BigDecimal leValue;

    /** 基线，最小值，低于该值触发告警 */
    @ApiModelProperty(value = "基线，最小值，低于该值触发告警", position = 16)
    private BigDecimal minValue;

    /** 基线，最大值，超过该值触发告警 */
    @ApiModelProperty(value = "基线，最大值，超过该值触发告警", position = 17)
    private BigDecimal maxValue;

    /** 条件值类型(取值同告警基本信息与告警条件关联表中条件值类型) */
    @ApiModelProperty(value = "条件值类型(取值同告警基本信息与告警条件关联表中条件值类型)", position = 18)
    private String valueType;

    /** 是否触发告警，0未触发，1触发 */
    @ApiModelProperty(value = "是否触发告警，0未触发，1触发", position = 19)
    private String alarm;

    /** 是否为强制告警，0未触发，1触发 */
    @ApiModelProperty(value = "是否为强制告警，0未触发，1触发", position = 20)
    private String forceAlarm;

    /** 统计数据类型 */
    @ApiModelProperty(value = "统计数据类型", position = 21)
    private String dataType;

    /** 描述 */
    @ApiModelProperty(value = "描述", position = 22)
    private String description;

    /** 日期(小时)的datetime类型 */
    @ApiModelProperty(value = "日期(小时)的datetime类型", position = 23)
    private Date dt;

    /** 状态，-1删除，0无效，1有效 */
    @ApiModelProperty(value = "状态，-1删除，0无效，1有效", position = 25)
    private String status;

}
