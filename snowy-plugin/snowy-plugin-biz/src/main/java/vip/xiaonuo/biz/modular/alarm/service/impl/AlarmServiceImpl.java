/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.alarm.service.impl;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.icepear.echarts.Line;
import org.icepear.echarts.charts.line.LineSeries;
import org.icepear.echarts.components.dataset.DataTransform;
import org.icepear.echarts.components.dataset.DataTransformConfig;
import org.icepear.echarts.components.dataset.Dataset;
import org.icepear.echarts.render.Engine;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.biz.core.config.AppConfig;
import vip.xiaonuo.biz.core.constant.DataSourceConstant;
import vip.xiaonuo.biz.modular.alarm.constant.MessageConstant;
import vip.xiaonuo.biz.modular.alarm.dto.*;
import vip.xiaonuo.biz.modular.alarm.entity.AlarmConditionLink;
import vip.xiaonuo.biz.modular.alarm.entity.AlarmLog;
import vip.xiaonuo.biz.modular.alarm.param.*;
import vip.xiaonuo.biz.modular.alarm.service.*;
import vip.xiaonuo.biz.modular.echarts.service.EChartsService;
import vip.xiaonuo.biz.modular.echarts.utils.EchartsJavaUtils;
import vip.xiaonuo.biz.modular.message.dto.SendEmailDTO;
import vip.xiaonuo.biz.modular.message.dto.SendRobotDTO;
import vip.xiaonuo.biz.modular.message.dto.SendXingeDTO;
import vip.xiaonuo.biz.modular.message.service.MessageService;
import vip.xiaonuo.biz.modular.monitor.entity.MonitorGameDay;
import vip.xiaonuo.biz.modular.monitor.service.MonitorGameDayService;
import vip.xiaonuo.biz.modular.remote.dto.EChartsExportParamDTO;
import vip.xiaonuo.biz.modular.tags.service.TagsService;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;
import vip.xiaonuo.biz.modular.alarm.entity.Alarm;
import vip.xiaonuo.biz.modular.alarm.mapper.AlarmMapper;
import vip.xiaonuo.common.pojo.CommonResult;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 告警基本信息Service接口实现类
 *
 * <AUTHOR>
 * @date 2023/03/31 11:07
 **/
@DS(DataSourceConstant.DYNAMIC_DATASOURCE_MONITOR)
@Service
@Slf4j
public class AlarmServiceImpl extends ServiceImpl<AlarmMapper, Alarm> implements AlarmService {

    @Autowired
    private ThresholdLogService thresholdLogService;

    @Autowired
    private ThresholdService thresholdService;

    @Autowired
    private MessageService messageService;

    @Autowired
    private AlarmLogService alarmLogService;

    @Autowired
    private AlarmConditionLinkService alarmConditionLinkService;

    @Autowired
    private MonitorGameDayService monitorGameDayService;

    @Autowired
    private EChartsService eChartsService;

    @Autowired
    private TagsService tagsService;

    @Override
    public Page<Alarm> page(AlarmPageParam alarmPageParam) {
        QueryWrapper<Alarm> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotEmpty(alarmPageParam.getAlarmName())) {
            queryWrapper.lambda().like(Alarm::getAlarmName, alarmPageParam.getAlarmName());
        }
        if (ObjectUtil.isNotEmpty(alarmPageParam.getAlarmType())) {
            queryWrapper.lambda().eq(Alarm::getAlarmType, alarmPageParam.getAlarmType());
        }
        if (ObjectUtil.isNotEmpty(alarmPageParam.getMpId())) {
            queryWrapper.lambda().eq(Alarm::getMpId, alarmPageParam.getMpId());
        }
        if (ObjectUtil.isNotEmpty(alarmPageParam.getStatus())) {
            queryWrapper.lambda().eq(Alarm::getStatus, alarmPageParam.getStatus());
        }
        if (ObjectUtil.isAllNotEmpty(alarmPageParam.getSortField(), alarmPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(alarmPageParam.getSortOrder());
            queryWrapper.orderBy(true, alarmPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(alarmPageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByDesc(Alarm::getCreateTime);
        }
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(AlarmAddParam alarmAddParam) {
        Alarm alarm = new Alarm();
        BeanUtils.copyProperties(alarmAddParam, alarm);
        // 告警基本信息ID
        String alarmId = IdWorker.getIdStr();
        alarm.setId(alarmId);
        if (CollectionUtils.isNotEmpty(alarmAddParam.getNotifyTypes())) {
            alarm.setNotifyType(JSONObject.toJSONString(alarmAddParam.getNotifyTypes()));
        } else {
            alarm.setNotifyType(JSONObject.toJSONString(new ArrayList<>()));
        }
        if (CollectionUtils.isNotEmpty(alarmAddParam.getReceiveUsers())) {
            alarm.setReceiveUser(JSONObject.toJSONString(alarmAddParam.getReceiveUsers()));
        } else {
            alarm.setReceiveUser(JSONObject.toJSONString(new ArrayList<>()));
        }
        if (CollectionUtils.isNotEmpty(alarmAddParam.getCcUsers())) {
            alarm.setCcUser(JSONObject.toJSONString(alarmAddParam.getCcUsers()));
        } else {
            alarm.setCcUser(JSONObject.toJSONString(new ArrayList<>()));
        }
        alarm.setCreateTime(new Date());
        alarm.setUpdateTime(new Date());
        // 状态，-1删除，0无效，1有效
        if (StringUtils.isBlank(alarm.getStatus())) {
            alarm.setStatus("1");
        }
        // 告警基本信息与告警条件关联信息
        List<AlarmConditionLinkAddParam> alarmConditionLinkAddParamList = alarmAddParam.getAlarmConditionLinkList();
        // 插入告警基本信息与告警条件关联表
        if (CollectionUtils.isNotEmpty(alarmConditionLinkAddParamList)) {
            List<AlarmConditionLink> alarmConditionLinkEntityList = new ArrayList<>();
            for (AlarmConditionLinkAddParam alarmConditionLinkAddParam : alarmConditionLinkAddParamList) {
                AlarmConditionLink alarmConditionLinkEntity = new AlarmConditionLink();
                BeanUtils.copyProperties(alarmConditionLinkAddParam, alarmConditionLinkEntity);
                // 告警基本信息ID
                alarmConditionLinkEntity.setAlarmId(alarmId);
                alarmConditionLinkEntity.setStatus(alarmConditionLinkAddParam.getAlarmConditionLinkStatus());
                // 状态，-1删除，0无效，1有效
                if (StringUtils.isBlank(alarmConditionLinkEntity.getStatus())) {
                    alarmConditionLinkEntity.setStatus("1");
                }
                alarmConditionLinkEntityList.add(alarmConditionLinkEntity);
            }
            alarmConditionLinkService.saveBatch(alarmConditionLinkEntityList);
        }

        log.debug(String.format("新增告警基本信息:[%s]", JSONObject.toJSONString(alarm)));
        this.save(alarm);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(AlarmEditParam alarmEditParam) {
        Alarm alarm = this.queryEntity(alarmEditParam.getId());
        BeanUtils.copyProperties(alarmEditParam, alarm);
        // 告警基本信息ID
        String alarmId = alarmEditParam.getId();
        if (CollectionUtils.isNotEmpty(alarmEditParam.getNotifyTypes())) {
            alarm.setNotifyType(JSONObject.toJSONString(alarmEditParam.getNotifyTypes()));
        } else {
            alarm.setNotifyType(JSONObject.toJSONString(new ArrayList<>()));
        }
        if (CollectionUtils.isNotEmpty(alarmEditParam.getReceiveUsers())) {
            alarm.setReceiveUser(JSONObject.toJSONString(alarmEditParam.getReceiveUsers()));
        } else {
            alarm.setReceiveUser(JSONObject.toJSONString(new ArrayList<>()));
        }
        if (CollectionUtils.isNotEmpty(alarmEditParam.getCcUsers())) {
            alarm.setCcUser(JSONObject.toJSONString(alarmEditParam.getCcUsers()));
        } else {
            alarm.setCcUser(JSONObject.toJSONString(new ArrayList<>()));
        }
        alarm.setUpdateTime(new Date());
        // 首先删除关联表
        alarmConditionLinkService.deleteByAlarmId(alarmId);

        // 告警基本信息与告警条件关联信息
        List<AlarmConditionLinkAddParam> alarmConditionLinkAddParamList = alarmEditParam.getAlarmConditionLinkList();
        // 插入告警基本信息与告警条件关联表
        if (CollectionUtils.isNotEmpty(alarmConditionLinkAddParamList)) {
            List<AlarmConditionLink> alarmConditionLinkEntityList = new ArrayList<>();
            for (AlarmConditionLinkAddParam alarmConditionLinkAddParam : alarmConditionLinkAddParamList) {
                AlarmConditionLink alarmConditionLinkEntity = new AlarmConditionLink();
                BeanUtils.copyProperties(alarmConditionLinkAddParam, alarmConditionLinkEntity);
                // 告警基本信息ID
                alarmConditionLinkEntity.setAlarmId(alarmId);
                alarmConditionLinkEntity.setStatus(alarmConditionLinkAddParam.getAlarmConditionLinkStatus());
                // 状态，-1删除，0无效，1有效
                if (StringUtils.isBlank(alarmConditionLinkEntity.getStatus())) {
                    alarmConditionLinkEntity.setStatus("1");
                }
                alarmConditionLinkEntityList.add(alarmConditionLinkEntity);
            }
            alarmConditionLinkService.saveBatch(alarmConditionLinkEntityList);
        }

        log.debug(String.format("更新告警基本信息:[%s]", JSONObject.toJSONString(alarm)));
        this.updateById(alarm);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<AlarmIdParam> alarmIdParamList) {
        // 首先删除关联表
        if (CollectionUtils.isNotEmpty(alarmIdParamList)) {
            for (AlarmIdParam alarmIdParam : alarmIdParamList) {
                alarmConditionLinkService.deleteByAlarmId(alarmIdParam.getId());
            }
        }
        // 执行删除
        this.removeBatchByIds(CollStreamUtil.toList(alarmIdParamList, AlarmIdParam::getId));
    }

    @Override
    public AlarmDetailDTO detail(AlarmIdParam alarmIdParam) {
        return this.baseMapper.getAlarmDetailById(alarmIdParam.getId());
    }

    @Override
    public Alarm queryEntity(String id) {
        Alarm alarm = this.getById(id);
        if (ObjectUtil.isEmpty(alarm)) {
            throw new CommonException("告警基本信息不存在，id值为：{}", id);
        }
        return alarm;
    }

    @Override
    public List<AlarmDetailDTO> listAlarmDetail(String alarmType, String alarmStatus, String linkStatus, String conditionStatus) {
        return this.baseMapper.listAlarmDetail(alarmType, alarmStatus, linkStatus, conditionStatus);
    }

    @Override
    public List<TemplateData> sendMonitorDayAlarm(String dayHour) {
        // 首先导入监控阈值及触发结果日志记录(天)
        List<TemplateData> templateDataList = thresholdLogService.importMonitorDayAlarmThresholdLog(dayHour);

        // 待发送的信鸽消息
        List<SendXingeDTO> sendXingeMessages = new ArrayList<>();
        // 待发送的邮件消息
        List<SendEmailDTO> sendEmailMessages = new ArrayList<>();
        // 待发送的机器人群消息
        List<SendRobotDTO> sendRobotDTOMessages = new ArrayList<>();
        // 告警日志记录
        List<AlarmLog> alarmLogList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(templateDataList)) {
            for (TemplateData templateData : templateDataList) {
                // 告警信息详情传输对象
                AlarmDetailDTO alarmDetailDTO = templateData.getAlarmDetailDTO();
                String day = templateData.getDay();
                Integer hour = templateData.getHour();
                // 数据无效
                Boolean notValidData = templateData.getNotValidData();
                String alarmId = alarmDetailDTO.getId();
                String alarmType = alarmDetailDTO.getAlarmType();
                Long mpId = alarmDetailDTO.getMpId();
                // TODO 数据类型从告警基本信息中获取
                String dataType = "0";
//                String dataType = alarmDetailDTO.getDataType();
                // 接收方式(=1邮件,=2钉钉,=3短信)
                List<String> notifyTypes = alarmDetailDTO.getNotifyTypes();
                if (templateData.getIsAlarm() && CollectionUtils.isNotEmpty(notifyTypes)) {
                    List<String> tagNameEnList = new ArrayList<>();
                    // 样本用户数据
                    List<SampleUserDTO> sampleUserDTOList = new ArrayList<>();
                    for (TriggerAlarmResultDTO triggerAlarmResultDTO : templateData.getTriggerAlarmResultDTOList()) {
                        CollectionUtils.addIgnoreNull(tagNameEnList, triggerAlarmResultDTO.getConditionDetailDTO().getConditionContent());
                        // 样本用户ID
                        MonitorGameDay monitorGameDay = triggerAlarmResultDTO.getMonitorGameDay();
                        String[] userIds = StringUtils.splitByWholeSeparatorPreserveAllTokens(monitorGameDay.getUserId(), ",");
                        if (userIds != null) {
                            SampleUserDTO sampleUserDTO = new SampleUserDTO();
                            sampleUserDTO.setConditionName(triggerAlarmResultDTO.getConditionDetailDTO().getConditionName());
                            sampleUserDTO.setUserIds(StringUtils.join(userIds, ", "));
                            Map<String, List<String>> userIdAndTagsMap = new LinkedHashMap<>();
                            // 查询标签
                            Map<String, List<String>> tagsMap = tagsService.listTags(Arrays.asList(userIds));
                            for (String userId : userIds) {
                                userIdAndTagsMap.put(userId, tagsMap.get(userId));
                            }
                            sampleUserDTO.setUserIdAndTagsMap(userIdAndTagsMap);
                            CollectionUtils.addIgnoreNull(sampleUserDTOList, sampleUserDTO);
                        }
                    }
                    templateData.setSampleUserDTOList(sampleUserDTOList);

                    // 历史数据
                    // 获取该应用最近7日游戏参赛情况数据
                    List<MonitorGameDay> monitorGameDayLastDaysList = monitorGameDayService.listLastDaysWithDefaultValue(alarmId, DateUtil.beginOfDay(DateUtil.offsetDay(DateUtil.parse(day), -6)).toString(DatePattern.NORM_DATE_PATTERN), day, hour, mpId, 1, 0, 1, tagNameEnList);
                    if (CollectionUtils.isNotEmpty(monitorGameDayLastDaysList)) {
                        LinkedHashMap<String, List<MonitorGameDay>> dayAndMonitorGameDayMap =
                                monitorGameDayLastDaysList.stream().collect(Collectors.groupingBy(MonitorGameDay::getDay, LinkedHashMap::new, Collectors.toList()));
                        templateData.setDayAndMonitorGameDayMap(dayAndMonitorGameDayMap);
                    }

                    // 图片
                    // 图片Base64字符串
                    String uidCntImageBase64 = this.generateMultiLineImageBase64(JSONObject.parseArray(JSONObject.toJSONString(monitorGameDayLastDaysList, SerializerFeature.WriteNullNumberAsZero)), "用户数", new String[]{"day", "uidCnt"}, "conditionName");
                    String incomeImageBase64 = this.generateMultiLineImageBase64(JSONObject.parseArray(JSONObject.toJSONString(monitorGameDayLastDaysList, SerializerFeature.WriteNullNumberAsZero)), "损益", new String[]{"day", "income"}, "conditionName");
                    String roundCntImageBase64 = this.generateMultiLineImageBase64(JSONObject.parseArray(JSONObject.toJSONString(monitorGameDayLastDaysList, SerializerFeature.WriteNullNumberAsZero)), "对局数", new String[]{"day", "roundCnt"}, "conditionName");
                    List<ImageFileNameDTO> imageFileNameDTOList = new ArrayList<>();
                    if (StringUtils.isNotBlank(uidCntImageBase64)) {
                        CollectionUtils.addIgnoreNull(imageFileNameDTOList, new ImageFileNameDTO().setFileName("异常用户触发趋势").setBase64(uidCntImageBase64));
                    }
                    if (StringUtils.isNotBlank(incomeImageBase64)) {
                        CollectionUtils.addIgnoreNull(imageFileNameDTOList, new ImageFileNameDTO().setFileName("损益变化趋势").setBase64(incomeImageBase64));
                    }
                    if (StringUtils.isNotBlank(roundCntImageBase64)) {
                        CollectionUtils.addIgnoreNull(imageFileNameDTOList, new ImageFileNameDTO().setFileName("对局数变化趋势").setBase64(roundCntImageBase64));
                    }
                    templateData.setImageFileNameDTOList(imageFileNameDTOList);

                    AlarmLog alarmLogEntity = new AlarmLog();
                    alarmLogEntity.setId(IdWorker.getIdStr());
                    alarmLogEntity.setDay(day);
                    alarmLogEntity.setHour(hour);
                    alarmLogEntity.setAlarmId(alarmId);
                    alarmLogEntity.setAlarmType(alarmType);
                    alarmLogEntity.setDataType(dataType);
                    alarmLogEntity.setMpId(mpId);
                    alarmLogEntity.setMpName(alarmDetailDTO.getDimProductInfo() == null ? null : alarmDetailDTO.getDimProductInfo().getMpName());
                    alarmLogEntity.setGameName(alarmDetailDTO.getDimProductInfo() == null ? null : alarmDetailDTO.getDimProductInfo().getGameName());
                    alarmLogEntity.setNotifyType(alarmDetailDTO.getNotifyType());
                    alarmLogEntity.setReceiveUser(alarmDetailDTO.getReceiveUser());
                    alarmLogEntity.setCcUser(alarmDetailDTO.getCcUser());
                    alarmLogEntity.setTemplateData(JSONObject.toJSONString(templateData, SerializerFeature.DisableCircularReferenceDetect));
                    alarmLogEntity.setAlarmConditions(JSONObject.toJSONString(templateData.getTriggerAlarmResultDTOList()));
                    alarmLogEntity.setDescription(alarmDetailDTO.getDescription());
                    alarmLogEntity.setDt(DateUtil.parse(day).offsetNew(DateField.HOUR_OF_DAY, hour).toJdkDate());
                    alarmLogEntity.setCreateTime(new Date());

                    // TODO 生成预览图片并上传小文件系统
//                    String imgPath = String.format("C:\\Users\\<USER>\\Desktop\\tmp\\nginx-1.20.0\\nginx-1.20.0\\html\\img\\%s.png", alarmLogEntity.getId());
//                    String previewImgUrl = String.format("http://************/img/%s.png", alarmLogEntity.getId());
//                    try {
//                        FileUtils.writeByteArrayToFile(new File(imgPath), org.apache.commons.codec.binary.Base64.decodeBase64(uidCntImageBase64));
//                        templateData.setPreviewImgUrl(previewImgUrl);
//                    } catch (IOException e) {
//                        log.error("写入图片文件失败!");
//                    }

                    Map<String, String> alarmMessagesMap = new HashMap<>();
                    Boolean isSendMessage;
                    // TODO 此处为多余的，天类型告警无需限制单日发送次数
                    // 统计周期(=1最近1小时,=2最近一天,=3最近一天(覆盖所有场地),=-1最近1小时(数据校验),=-2最近一天(数据校验))
                    // 数据校验无需判断是否超过单日最大告警次数，非数据校验超过单日最大告警次数将不发送
                    if (StringUtils.equalsIgnoreCase("-2", alarmType)) {
                        isSendMessage = Boolean.TRUE;
                    } else {
                        isSendMessage = Boolean.TRUE;
//                        // 是否发送告警消息
//                        Map<String, Long> alarmLogCntMap = alarmLogService.listAlarmLogCntMap(day, day, new ArrayList<>(Arrays.asList(1)));
//                        // 当天是否已经成功发送告警2次
//                        isSendMessage = alarmLogCntMap.getOrDefault(alarmId, 0L).compareTo(2L) >= 0 ? Boolean.FALSE : Boolean.TRUE;
                    }
                    // 如果触发告警则生成告警消息
                    // 接收方式(=1邮件,=2钉钉,=3机器人群消息)
                    if (CollectionUtils.containsAny(notifyTypes, "1")) {
                        SendEmailDTO sendEmailDTO = messageService.generateSendEmailMessage(MessageConstant.SEND_EMAIL_MONITOR_MESSAGE_TEMPLATE, templateData);
                        templateData.setSendEmailMessage(sendEmailDTO);
                        alarmMessagesMap.put("sendEmailMessage", sendEmailDTO.getBodyNotBase64());
                        // 数据合法并且需要发送
                        if ((!notValidData) && isSendMessage) {
//                            CollectionUtils.addIgnoreNull(sendEmailMessages, sendEmailDTO);
                            this.splitSecTeamUsers(sendEmailMessages, sendEmailDTO);
                        }
                    }
                    if (CollectionUtils.containsAny(notifyTypes, "2")) {
                        SendXingeDTO sendXingeDTO = messageService.generateSendXingeMessage(MessageConstant.SEND_XINGE_MONITOR_MESSAGE_TEMPLATE, templateData);
                        templateData.setSendXingeMessage(sendXingeDTO);
                        alarmMessagesMap.put("sendXingeMessage", sendXingeDTO.getBodyNotBase64());
                        //  数据合法并且需要发送
                        if ((!notValidData) && isSendMessage) {
                            CollectionUtils.addIgnoreNull(sendXingeMessages, sendXingeDTO);
                        }
                    }
                    // 群消息
                    if (CollectionUtils.containsAny(notifyTypes, "3")) {
                        SendRobotDTO sendRobotDTO = messageService.generateSendDDRobotMessage(MessageConstant.SEND_ROBOT_MONITOR_MESSAGE_TEMPLATE, templateData);
                        //  数据合法并且需要发送
                        if ((!notValidData) && isSendMessage) {
                            CollectionUtils.addIgnoreNull(sendRobotDTOMessages, sendRobotDTO);
                        }
                    }
                    alarmLogEntity.setAlarmMessages(JSONObject.toJSONString(alarmMessagesMap));
//                    // 状态，=0发送失败，=1发送成功，=2不发送该条告警,=-1不发送该条告警(数据异常)
                    alarmLogEntity.setStatus(BooleanUtils.toString(notValidData, "-1", BooleanUtils.toString(isSendMessage, "1", "2")));
                    CollectionUtils.addIgnoreNull(alarmLogList, alarmLogEntity);
                }
            }
        }

        // 发送消息
        messageService.sendXingeV2(sendXingeMessages);
        messageService.sendEmail(sendEmailMessages);
        messageService.sendDDRobot(sendRobotDTOMessages);
        // 保存告警日志
        alarmLogService.add(alarmLogList);
        return templateDataList;
    }

    @Override
    public List<TemplateData> sendMonitorDayAlarmAllMpid(String dayHour) {
        // 首先计算监控阈值及触发结果(天)
        List<TemplateData> templateDataList = thresholdService.listMonitorDayTemplateDataAllMpid(dayHour);

        // 待发送的信鸽消息
        List<SendXingeDTO> sendXingeMessages = new ArrayList<>();
        // 待发送的邮件消息
        List<SendEmailDTO> sendEmailMessages = new ArrayList<>();
        // 待发送的机器人群消息
        List<SendRobotDTO> sendRobotDTOMessages = new ArrayList<>();
        // 告警日志记录
        List<AlarmLog> alarmLogList = new ArrayList<>();
        // 按照分组ID合并的模板数据
        Map<String, MergedTemplateDataDTO> groupIdToMergedDataMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(templateDataList)) {
            for (TemplateData templateData : templateDataList) {
                // 告警信息详情传输对象
                AlarmDetailDTO alarmDetailDTO = templateData.getAlarmDetailDTO();
                String day = templateData.getDay();
                Integer hour = templateData.getHour();
                // 数据无效
                Boolean notValidData = templateData.getNotValidData();
                String alarmId = alarmDetailDTO.getId();
                String alarmType = alarmDetailDTO.getAlarmType();
                Long mpId = alarmDetailDTO.getMpId();
                // TODO 数据类型从告警基本信息中获取
                String dataType = "0";
//                String dataType = alarmDetailDTO.getDataType();
                // 接收方式(=1邮件,=2钉钉,=3短信)
                List<String> notifyTypes = alarmDetailDTO.getNotifyTypes();
                if (templateData.getIsAlarm() && CollectionUtils.isNotEmpty(notifyTypes)) {
                    List<String> tagNameEnList = new ArrayList<>();
                    // 样本用户数据
                    List<SampleUserDTO> sampleUserDTOList = new ArrayList<>();
                    for (TriggerAlarmResultDTO triggerAlarmResultDTO : templateData.getTriggerAlarmResultDTOList()) {
                        CollectionUtils.addIgnoreNull(tagNameEnList, triggerAlarmResultDTO.getConditionDetailDTO().getConditionContent());
                        // 样本用户ID
                        MonitorGameDay monitorGameDay = triggerAlarmResultDTO.getMonitorGameDay();
                        String[] userIds = StringUtils.splitByWholeSeparatorPreserveAllTokens(monitorGameDay.getUserId(), ",");
                        if (userIds != null) {
                            SampleUserDTO sampleUserDTO = new SampleUserDTO();
                            sampleUserDTO.setConditionName(triggerAlarmResultDTO.getConditionDetailDTO().getConditionName());
                            sampleUserDTO.setUserIds(StringUtils.join(userIds, ", "));
                            Map<String, List<String>> userIdAndTagsMap = new LinkedHashMap<>();
                            // 查询标签
                            Map<String, List<String>> tagsMap = tagsService.listTags(Arrays.asList(userIds));
                            for (String userId : userIds) {
                                userIdAndTagsMap.put(userId, tagsMap.get(userId));
                            }
                            sampleUserDTO.setUserIdAndTagsMap(userIdAndTagsMap);
                            CollectionUtils.addIgnoreNull(sampleUserDTOList, sampleUserDTO);
                        }
                    }
                    templateData.setSampleUserDTOList(sampleUserDTOList);

                    // 历史数据
                    // 获取该应用最近7日游戏参赛情况数据
                    List<MonitorGameDay> monitorGameDayLastDaysList = monitorGameDayService.listLastDaysWithDefaultValue(alarmId, DateUtil.beginOfDay(DateUtil.offsetDay(DateUtil.parse(day), -6)).toString(DatePattern.NORM_DATE_PATTERN), day, hour, mpId, 1, 0, 1, tagNameEnList);
                    if (CollectionUtils.isNotEmpty(monitorGameDayLastDaysList)) {
                        LinkedHashMap<String, List<MonitorGameDay>> dayAndMonitorGameDayMap =
                                monitorGameDayLastDaysList.stream().collect(Collectors.groupingBy(MonitorGameDay::getDay, LinkedHashMap::new, Collectors.toList()));
                        templateData.setDayAndMonitorGameDayMap(dayAndMonitorGameDayMap);
                    }

                    // 图片
                    // 图片Base64字符串
                    String uidCntImageBase64 = this.generateMultiLineImageBase64(JSONObject.parseArray(JSONObject.toJSONString(monitorGameDayLastDaysList, SerializerFeature.WriteNullNumberAsZero)), "用户数", new String[]{"day", "uidCnt"}, "conditionName");
                    String incomeImageBase64 = this.generateMultiLineImageBase64(JSONObject.parseArray(JSONObject.toJSONString(monitorGameDayLastDaysList, SerializerFeature.WriteNullNumberAsZero)), "损益", new String[]{"day", "income"}, "conditionName");
                    String roundCntImageBase64 = this.generateMultiLineImageBase64(JSONObject.parseArray(JSONObject.toJSONString(monitorGameDayLastDaysList, SerializerFeature.WriteNullNumberAsZero)), "对局数", new String[]{"day", "roundCnt"}, "conditionName");
                    List<ImageFileNameDTO> imageFileNameDTOList = new ArrayList<>();
                    if (StringUtils.isNotBlank(uidCntImageBase64)) {
                        CollectionUtils.addIgnoreNull(imageFileNameDTOList, new ImageFileNameDTO().setFileName("异常用户触发趋势").setBase64(uidCntImageBase64));
                    }
                    if (StringUtils.isNotBlank(incomeImageBase64)) {
                        CollectionUtils.addIgnoreNull(imageFileNameDTOList, new ImageFileNameDTO().setFileName("损益变化趋势").setBase64(incomeImageBase64));
                    }
                    if (StringUtils.isNotBlank(roundCntImageBase64)) {
                        CollectionUtils.addIgnoreNull(imageFileNameDTOList, new ImageFileNameDTO().setFileName("对局数变化趋势").setBase64(roundCntImageBase64));
                    }
                    templateData.setImageFileNameDTOList(imageFileNameDTOList);

                    AlarmLog alarmLogEntity = new AlarmLog();
                    alarmLogEntity.setId(IdWorker.getIdStr());
                    alarmLogEntity.setDay(day);
                    alarmLogEntity.setHour(hour);
                    alarmLogEntity.setAlarmId(alarmId);
                    alarmLogEntity.setAlarmType(alarmType);
                    alarmLogEntity.setDataType(dataType);
                    alarmLogEntity.setMpId(mpId);
                    alarmLogEntity.setMpName(alarmDetailDTO.getDimProductInfo() == null ? null : alarmDetailDTO.getDimProductInfo().getMpName());
                    alarmLogEntity.setGameName(alarmDetailDTO.getDimProductInfo() == null ? null : alarmDetailDTO.getDimProductInfo().getGameName());
                    alarmLogEntity.setNotifyType(alarmDetailDTO.getNotifyType());
                    alarmLogEntity.setReceiveUser(alarmDetailDTO.getReceiveUser());
                    alarmLogEntity.setCcUser(alarmDetailDTO.getCcUser());
                    alarmLogEntity.setTemplateData(JSONObject.toJSONString(templateData, SerializerFeature.DisableCircularReferenceDetect));
                    alarmLogEntity.setAlarmConditions(JSONObject.toJSONString(templateData.getTriggerAlarmResultDTOList()));
                    alarmLogEntity.setDescription(alarmDetailDTO.getDescription());
                    alarmLogEntity.setDt(DateUtil.parse(day).offsetNew(DateField.HOUR_OF_DAY, hour).toJdkDate());
                    alarmLogEntity.setCreateTime(new Date());

                    Map<String, String> alarmMessagesMap = new HashMap<>();
                    Boolean isSendMessage;
                    // TODO 此处为多余的，天类型告警无需限制单日发送次数
                    // 统计周期(=1最近1小时,=2最近一天,=3最近一天(覆盖所有场地),=-1最近1小时(数据校验),=-2最近一天(数据校验))
                    // 数据校验无需判断是否超过单日最大告警次数，非数据校验超过单日最大告警次数将不发送
                    if (StringUtils.equalsIgnoreCase("-2", alarmType)) {
                        isSendMessage = Boolean.TRUE;
                    } else {
                        isSendMessage = Boolean.TRUE;
//                        // 是否发送告警消息
//                        Map<String, Long> alarmLogCntMap = alarmLogService.listAlarmLogCntMap(day, day, new ArrayList<>(Arrays.asList(1)));
//                        // 当天是否已经成功发送告警2次
//                        isSendMessage = alarmLogCntMap.getOrDefault(alarmId, 0L).compareTo(2L) >= 0 ? Boolean.FALSE : Boolean.TRUE;
                    }
                    // 如果触发告警则生成告警消息
                    // 接收方式(=1邮件,=2钉钉,=3机器人群消息)
                    if (CollectionUtils.containsAny(notifyTypes, "1")) {
                        SendEmailDTO sendEmailDTO = messageService.generateSendEmailMessage(MessageConstant.SEND_EMAIL_MONITOR_MESSAGE_TEMPLATE, templateData);
                        templateData.setSendEmailMessage(sendEmailDTO);
                        alarmMessagesMap.put("sendEmailMessage", sendEmailDTO.getBodyNotBase64());
                        // 数据合法并且需要发送
                        if ((!notValidData) && isSendMessage) {
                            // 如果有分组ID，则按分组ID合并发送
                            String groupId = alarmDetailDTO.getGroupId();
                            if (StringUtils.isNotBlank(groupId)) {
                                // 将模板数据添加到对应的分组中
                                MergedTemplateDataDTO mergedData = groupIdToMergedDataMap.computeIfAbsent(groupId, k -> {
                                    MergedTemplateDataDTO newMergedData = new MergedTemplateDataDTO();
                                    newMergedData.setGroupId(groupId);
                                    newMergedData.setGroupName(alarmDetailDTO.getGroupName());
                                    newMergedData.setAlarmTime(DateUtil.now());
                                    newMergedData.setTemplateDataList(new ArrayList<>());
                                    newMergedData.setEmailTo(new ArrayList<>());
                                    newMergedData.setEmailCc(new ArrayList<>());
                                    return newMergedData;
                                });

                                // 添加模板数据
                                mergedData.getTemplateDataList().add(templateData);

                                // 添加收件人和抄送人
                                if (CollectionUtils.isNotEmpty(sendEmailDTO.getEmailTo())) {
                                    for (String emailTo : sendEmailDTO.getEmailTo()) {
                                        if (!mergedData.getEmailTo().contains(emailTo)) {
                                            mergedData.getEmailTo().add(emailTo);
                                        }
                                    }
                                }
                                if (CollectionUtils.isNotEmpty(sendEmailDTO.getEmailCC())) {
                                    for (String emailCc : sendEmailDTO.getEmailCC()) {
                                        if (!mergedData.getEmailCc().contains(emailCc)) {
                                            mergedData.getEmailCc().add(emailCc);
                                        }
                                    }
                                }
                            } else {
                                // 没有分组ID，使用原来的方式发送
                                // 覆盖所有场地的告警不拆分
                                if (BooleanUtils.toBooleanDefaultIfNull(templateData.getIsAllMpid(), Boolean.FALSE)) {
                                    CollectionUtils.addIgnoreNull(sendEmailMessages, sendEmailDTO);
                                } else {
                                    this.splitSecTeamUsers(sendEmailMessages, sendEmailDTO);
                                }
                            }
                        }
                    }
                    if (CollectionUtils.containsAny(notifyTypes, "2")) {
                        SendXingeDTO sendXingeDTO = messageService.generateSendXingeMessage(MessageConstant.SEND_XINGE_MONITOR_MESSAGE_TEMPLATE, templateData);
                        templateData.setSendXingeMessage(sendXingeDTO);
                        alarmMessagesMap.put("sendXingeMessage", sendXingeDTO.getBodyNotBase64());
                        //  数据合法并且需要发送
                        if ((!notValidData) && isSendMessage) {
                            CollectionUtils.addIgnoreNull(sendXingeMessages, sendXingeDTO);
                        }
                    }
                    // 群消息
                    if (CollectionUtils.containsAny(notifyTypes, "3")) {
                        SendRobotDTO sendRobotDTO = messageService.generateSendDDRobotMessage(MessageConstant.SEND_ROBOT_MONITOR_MESSAGE_TEMPLATE, templateData);
                        //  数据合法并且需要发送
                        if ((!notValidData) && isSendMessage) {
                            CollectionUtils.addIgnoreNull(sendRobotDTOMessages, sendRobotDTO);
                        }
                    }
                    alarmLogEntity.setAlarmMessages(JSONObject.toJSONString(alarmMessagesMap));
                    // 状态，=0发送失败，=1发送成功，=2不发送该条告警,=-1不发送该条告警(数据异常)
                    alarmLogEntity.setStatus(BooleanUtils.toString(notValidData, "-1", BooleanUtils.toString(isSendMessage, "1", "2")));
                    CollectionUtils.addIgnoreNull(alarmLogList, alarmLogEntity);
                }
            }
        }

        // 处理合并的模板数据，生成合并邮件
        if (!groupIdToMergedDataMap.isEmpty()) {
            for (MergedTemplateDataDTO mergedData : groupIdToMergedDataMap.values()) {
                if (CollectionUtils.isNotEmpty(mergedData.getTemplateDataList())) {
                    // 生成合并邮件消息
                    SendEmailDTO mergedEmailDTO = messageService.generateSendMergedEmailMessage(
                            MessageConstant.SEND_EMAIL_MONITOR_MERGED_MESSAGE_TEMPLATE,
                            mergedData);
                    // TODO 测试
                    try {
                        FileUtils.writeStringToFile(new File("C:\\Users\\<USER>\\Desktop\\test\\合并邮件_" + IdWorker.getTimeId() + ".html"), mergedEmailDTO.getBodyNotBase64(), "UTF-8");
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                    // 添加到发送列表
                    // 不拆分
                    // CollectionUtils.addIgnoreNull(sendEmailMessages, mergedEmailDTO);
                    // 拆分
                    this.splitSecTeamUsers(sendEmailMessages, mergedEmailDTO);
                }
            }
        }

        // 发送消息
//        messageService.sendXingeV2(sendXingeMessages);
        messageService.sendEmail(sendEmailMessages);
//        messageService.sendDDRobot(sendRobotDTOMessages);
        // 保存告警日志
//        alarmLogService.add(alarmLogList);
        return templateDataList;
    }

    // 图片生成
    // 详细数据(天)图片
    private String generateDayImageBase64(Object dataList, String titleText, String[] dimensions, String[] lineSeriesNames) {

        // ECharts Export Server服务器地址(如果为空则关闭图片导出功能)
        String url = AppConfig.ECHARTS_EXPORT_SERVER_URL;
        if (StringUtils.isBlank(url) || ArrayUtils.isEmpty(dimensions) || ArrayUtils.isEmpty(lineSeriesNames)) {
            log.warn("ECharts Export Server图片导出功能关闭或者导出数据为空，将不执行图片导出!");
            return null;
        }

        Line line = EchartsJavaUtils.defaultLineStyle(titleText, null, null, "日期", null);

        // 原始数据，该dataset 的 index 为 0
        Dataset dataset = new Dataset();
//        dimensions = new String[]{"month", "avgRateUserTotal", "avgRateUserCrash", "avgRateUserCaton", "avgRateUserError", "avgRateUserReport"};
//        lineSeriesNames = new String[]{"用户异常率", "崩溃影响用户率", "卡顿影响用户率", "错误影响用户率", "已捕获异常影响用户率"};
        dataset.setDimensions(dimensions);
        dataset.setSource(dataList);
        line.addDataset(dataset);
        // 使用sort data transform对数据进行排序后的数据，该dataset 的 index 为 1
        Dataset datasetTransform = new Dataset();
        datasetTransform.setTransform(new DataTransform().setType("sort").setConfig(new DataTransformConfig().setDimension("day").setOrder("asc")));
        line.addDataset(datasetTransform);

        for (String lineSeriesName : lineSeriesNames) {
            LineSeries lineSeries = EchartsJavaUtils.defaultLineSeriesStyle();
            lineSeries.setName(lineSeriesName);
            // 该 series 引用 index 为 1 的 dataset (排序后的数据)
            lineSeries.setDatasetIndex(1);
            line.addSeries(lineSeries);
        }

        Engine engine = new Engine();
        // The renderJsonOption method will return a string, which represents an Option object in JSON format.
        String optionString = engine.renderJsonOption(line);
        EChartsExportParamDTO eChartsExportParamDTO = new EChartsExportParamDTO();
        eChartsExportParamDTO.setBase64(Boolean.TRUE);
        eChartsExportParamDTO.setDownload(Boolean.FALSE);
        eChartsExportParamDTO.setWidth(820);
        eChartsExportParamDTO.setHeight(410);
        eChartsExportParamDTO.setOption(JSONObject.parseObject(optionString));

        CommonResult<String> result = eChartsService.generateChart(eChartsExportParamDTO);
        if (Integer.valueOf(CommonResult.CODE_SUCCESS).equals(result.getCode())) {
            String base64 = result.getData();
            return StringUtils.substringAfter(base64, ",");
        } else {
            log.warn("生成图片失败!result=[{}]", JSONObject.toJSONString(result));
            return null;
        }
    }

    private String generateMultiLineImageBase64(JSONArray source, String titleText, String[] dimensions, String lineDimension) {

        // ECharts Export Server服务器地址(如果为空则关闭图片导出功能)
        String url = AppConfig.ECHARTS_EXPORT_SERVER_URL;
        if (StringUtils.isBlank(url) || CollectionUtils.isEmpty(source) || ArrayUtils.isEmpty(dimensions) || StringUtils.isBlank(lineDimension)) {
            log.warn("ECharts Export Server图片导出功能关闭或者导出数据为空，将不执行图片导出!");
            return null;
        }
        Set<String> lineDimensionValueSet = new TreeSet<>();
        for (int i = 0; i < source.size(); i++) {
            lineDimensionValueSet.add(source.getJSONObject(i).getString(lineDimension));
        }
        List<String> lineDimensionValueList = new ArrayList<>(lineDimensionValueSet);

        Line line = EchartsJavaUtils.defaultLineStyle(titleText, null, null, "日期", null);

        // 原始数据，该dataset 的 index 为 0
        Dataset dataset = new Dataset();
        dataset.setSource(source);
        line.addDataset(dataset);
        for (String lineDimensionValue : lineDimensionValueList) {
            // 过滤相应版本，并使用sort data transform对数据进行排序后的数据，该dataset 的 index 为 从1开始
            Dataset datasetTransform = new Dataset();
            datasetTransform.setTransform(new DataTransform[]{new DataTransform().setType("filter").setConfig(new DataTransformConfig().setDimension(lineDimension).setValue(lineDimensionValue)), new DataTransform().setType("sort").setConfig(new DataTransformConfig().setDimension("day").setOrder("asc"))});
            line.addDataset(datasetTransform);
        }

        for (int i = 0; i < lineDimensionValueList.size(); i++) {
            LineSeries lineSeries = EchartsJavaUtils.defaultLineSeriesStyle();
            lineSeries.setName(lineDimensionValueList.get(i));
            lineSeries.setDimensions(dimensions);
            // 该 series 引用 index 为 1 的 dataset (排序后的数据)
            lineSeries.setDatasetIndex((i + 1));
            line.addSeries(lineSeries);
        }

        Engine engine = new Engine();
        // The renderJsonOption method will return a string, which represents an Option object in JSON format.
        String optionString = engine.renderJsonOption(line);
        EChartsExportParamDTO eChartsExportParamDTO = new EChartsExportParamDTO();
        eChartsExportParamDTO.setBase64(Boolean.TRUE);
        eChartsExportParamDTO.setDownload(Boolean.FALSE);
        eChartsExportParamDTO.setWidth(820);
        eChartsExportParamDTO.setHeight(410);
        eChartsExportParamDTO.setOption(JSONObject.parseObject(optionString));

        CommonResult<String> result = eChartsService.generateChart(eChartsExportParamDTO);
        if (Integer.valueOf(CommonResult.CODE_SUCCESS).equals(result.getCode())) {
            String base64 = result.getData();
            return StringUtils.substringAfter(base64, ",");
        } else {
            log.warn("生成图片失败!result=[{}]", JSONObject.toJSONString(result));
            return null;
        }
    }

    @Override
    public void editStatus(String id, String status) {
        Alarm alarm = this.queryEntity(id);
        alarm.setStatus(status);
        this.updateById(alarm);
    }

    private void splitSecTeamUsers(List<SendEmailDTO> sendEmailMessages, SendEmailDTO sendEmailDTO) {
        // TODO 是否拆分安全团队
        Boolean secTeam = Boolean.TRUE;
        if (secTeam) {
            // 所有安全团队人员
            List<String> secTeamEmailToAll = Arrays.asList("7572", "5006", "10300", "8325", "4369");
            List<String> secTeamEmailCCAll = Arrays.asList("98", "5213", "5323", "8257");

            // 安全团队人员发送列表
            List<String> secEmailTo = new ArrayList<>();
            List<String> secEmailCC = new ArrayList<>();
            // 非安全团队人员发送列表
            List<String> notSecEmailTo = new ArrayList<>();
            List<String> notSecEmailCC = new ArrayList<>();

            // 筛选出安全团队人员和非安全团队人员
            if (CollectionUtils.isNotEmpty(sendEmailDTO.getEmailTo())) {
                for (String userIdTmp : sendEmailDTO.getEmailTo()) {
                    if (CollectionUtils.isNotEmpty(secTeamEmailToAll) && secTeamEmailToAll.contains(userIdTmp)) {
                        // 安全团队人员(主送)
                        CollectionUtils.addIgnoreNull(secEmailTo, userIdTmp);
                    } else if (CollectionUtils.isNotEmpty(secTeamEmailCCAll) && secTeamEmailCCAll.contains(userIdTmp)) {
                        // 安全团队人员(抄送)
                        CollectionUtils.addIgnoreNull(secEmailCC, userIdTmp);
                    } else {
                        // 非安全团队人员
                        CollectionUtils.addIgnoreNull(notSecEmailTo, userIdTmp);
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(sendEmailDTO.getEmailCC())) {
                for (String userIdTmp : sendEmailDTO.getEmailCC()) {
                    if (CollectionUtils.isNotEmpty(secTeamEmailToAll) && secTeamEmailToAll.contains(userIdTmp)) {
                        // 安全团队人员(主送)
                        CollectionUtils.addIgnoreNull(secEmailTo, userIdTmp);
                    } else if (CollectionUtils.isNotEmpty(secTeamEmailCCAll) && secTeamEmailCCAll.contains(userIdTmp)) {
                        // 安全团队人员(抄送)
                        CollectionUtils.addIgnoreNull(secEmailCC, userIdTmp);
                    } else {
                        CollectionUtils.addIgnoreNull(notSecEmailCC, userIdTmp);
                    }
                }
            }

            // 安全团队发送列表
            if (CollectionUtils.isEmpty(secEmailTo) && CollectionUtils.isNotEmpty(secEmailCC)) {
                CollectionUtils.addAll(secEmailTo, AppConfig.SYSTEM_ADMINISTRATOR_USERIDS);
            }
            SendEmailDTO secSendEmailDTO = new SendEmailDTO();
            BeanUtils.copyProperties(sendEmailDTO, secSendEmailDTO);
            secSendEmailDTO.setEmailTo(secEmailTo);
            secSendEmailDTO.setEmailCC(secEmailCC);
            CollectionUtils.addIgnoreNull(sendEmailMessages, secSendEmailDTO);

            // 非安全团队发送列表
            if (CollectionUtils.isEmpty(notSecEmailTo) && CollectionUtils.isNotEmpty(notSecEmailCC)) {
                CollectionUtils.addAll(notSecEmailTo, AppConfig.SYSTEM_ADMINISTRATOR_USERIDS);
            }
            CollectionUtils.addAll(notSecEmailCC, AppConfig.SYSTEM_ADMINISTRATOR_USERIDS);
            CollectionUtils.addAll(notSecEmailCC, Arrays.asList("5323"));
            SendEmailDTO notSecSendEmailDTO = new SendEmailDTO();
            BeanUtils.copyProperties(sendEmailDTO, notSecSendEmailDTO);
            notSecSendEmailDTO.setEmailTo(notSecEmailTo);
            notSecSendEmailDTO.setEmailCC(notSecEmailCC);
            CollectionUtils.addIgnoreNull(sendEmailMessages, notSecSendEmailDTO);
            log.info("拆分结果：原始主送={},原始抄送={},安全主送={}，安全抄送={},非安全主送={},非安全抄送={}", JSONObject.toJSONString(sendEmailDTO.getEmailTo()), JSONObject.toJSONString(sendEmailDTO.getEmailCC()), JSONObject.toJSONString(secEmailTo), JSONObject.toJSONString(secEmailCC), JSONObject.toJSONString(notSecEmailTo), JSONObject.toJSONString(notSecEmailCC));
        } else {
            CollectionUtils.addIgnoreNull(sendEmailMessages, sendEmailDTO);
        }
    }
}
