/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.alarm.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.math3.stat.StatUtils;
import org.apache.commons.math3.util.FastMath;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.biz.core.constant.CommonConstant;
import vip.xiaonuo.biz.core.constant.DataSourceConstant;
import vip.xiaonuo.biz.modular.alarm.dto.*;
import vip.xiaonuo.biz.modular.alarm.entity.ThresholdLog;
import vip.xiaonuo.biz.modular.alarm.enums.AlarmConditionValueTypeEnum;
import vip.xiaonuo.biz.modular.alarm.mapper.ThresholdLogMapper;
import vip.xiaonuo.biz.modular.alarm.param.ThresholdLogAddParam;
import vip.xiaonuo.biz.modular.alarm.param.ThresholdLogEditParam;
import vip.xiaonuo.biz.modular.alarm.param.ThresholdLogIdParam;
import vip.xiaonuo.biz.modular.alarm.param.ThresholdLogPageParam;
import vip.xiaonuo.biz.modular.alarm.service.AlarmService;
import vip.xiaonuo.biz.modular.alarm.service.ThresholdLogService;
import vip.xiaonuo.biz.modular.alarm.utils.MonitorDataUtils;
import vip.xiaonuo.biz.modular.data.service.MpsService;
import vip.xiaonuo.biz.modular.monitor.entity.MonitorGameDay;
import vip.xiaonuo.biz.modular.monitor.service.MonitorGameDayService;
import vip.xiaonuo.common.cache.CommonCacheOperator;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 监控阈值及触发结果日志记录Service接口实现类
 *
 * <AUTHOR>
 * @date 2023/03/31 11:18
 **/
@Service
@Slf4j
@DS(DataSourceConstant.DYNAMIC_DATASOURCE_MONITOR)
public class ThresholdLogServiceImpl extends ServiceImpl<ThresholdLogMapper, ThresholdLog> implements ThresholdLogService {

    @Autowired
    private AlarmService alarmService;

    @Autowired
    private MonitorGameDayService monitorGameDayService;

    @Autowired
    private MpsService mpsService;

    private static final String HISTORICAL_MONITOR_GAME_DAY_DATA_CACHE_KEY = "historicalMonitorGameDayData:";

    @Autowired
    private CommonCacheOperator commonCacheOperator;

    @Override
    public Page<ThresholdLog> page(ThresholdLogPageParam thresholdLogPageParam) {
        QueryWrapper<ThresholdLog> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotEmpty(thresholdLogPageParam.getDay())) {
            queryWrapper.lambda().eq(ThresholdLog::getDay, thresholdLogPageParam.getDay());
        }
        if (ObjectUtil.isNotEmpty(thresholdLogPageParam.getAlarmId())) {
            queryWrapper.lambda().eq(ThresholdLog::getAlarmId, thresholdLogPageParam.getAlarmId());
        }
        if (ObjectUtil.isNotEmpty(thresholdLogPageParam.getMpId())) {
            queryWrapper.lambda().eq(ThresholdLog::getMpId, thresholdLogPageParam.getMpId());
        }
        if (ObjectUtil.isNotEmpty(thresholdLogPageParam.getStatus())) {
            queryWrapper.lambda().eq(ThresholdLog::getStatus, thresholdLogPageParam.getStatus());
        }
        if (ObjectUtil.isAllNotEmpty(thresholdLogPageParam.getSortField(), thresholdLogPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(thresholdLogPageParam.getSortOrder());
            queryWrapper.orderBy(true, thresholdLogPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(thresholdLogPageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByDesc(ThresholdLog::getId);
        }
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Override
    public void add(ThresholdLogAddParam thresholdLogAddParam) {
        ThresholdLog thresholdLog = BeanUtil.toBean(thresholdLogAddParam, ThresholdLog.class);
        this.save(thresholdLog);
    }

    @Override
    public void edit(ThresholdLogEditParam thresholdLogEditParam) {
        ThresholdLog thresholdLog = this.queryEntity(thresholdLogEditParam.getId());
        BeanUtil.copyProperties(thresholdLogEditParam, thresholdLog);
        this.updateById(thresholdLog);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<ThresholdLogIdParam> thresholdLogIdParamList) {
        // 执行删除
        this.removeBatchByIds(CollStreamUtil.toList(thresholdLogIdParamList, ThresholdLogIdParam::getId));
    }

    @Override
    public ThresholdLog detail(ThresholdLogIdParam thresholdLogIdParam) {
        return this.queryEntity(thresholdLogIdParam.getId());
    }

    @Override
    public ThresholdLog queryEntity(String id) {
        ThresholdLog thresholdLog = this.getById(id);
        if (ObjectUtil.isEmpty(thresholdLog)) {
            throw new CommonException("监控阈值及触发结果日志记录不存在，id值为：{}", id);
        }
        return thresholdLog;
    }

    @Override
    public List<TemplateData> importMonitorDayAlarmThresholdLog(String dayHour) {
        // 获取告警结果数据(天)
        List<TemplateData> templateDataList = this.listMonitorDayTemplateData(dayHour);
        if (CollectionUtils.isNotEmpty(templateDataList)) {
            List<ThresholdLog> thresholdLogEntityList = new ArrayList<>();
            for (TemplateData templateData : templateDataList) {
                // 告警信息详情传输对象
                AlarmDetailDTO alarmDetailDTO = templateData.getAlarmDetailDTO();
                // 该条告警信息的触发结果
                List<TriggerAlarmResultDTO> allAlarmResultDTOList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(templateData.getTriggerAlarmResultDTOList())) {
                    CollectionUtils.addAll(allAlarmResultDTOList, templateData.getTriggerAlarmResultDTOList());
                }
                if (CollectionUtils.isNotEmpty(templateData.getNotTriggerAlarmResultDTOList())) {
                    CollectionUtils.addAll(allAlarmResultDTOList, templateData.getNotTriggerAlarmResultDTOList());
                }

                String day = templateData.getDay();
                Integer hour = templateData.getHour();
                String alarmId = alarmDetailDTO.getId();
                String alarmType = alarmDetailDTO.getAlarmType();
                Long mpId = alarmDetailDTO.getMpId();
                // 首先删除该告警信息ID、该小时的数据
                this.delete(alarmId, day, hour);
                if (CollectionUtils.isNotEmpty(allAlarmResultDTOList)) {
                    for (TriggerAlarmResultDTO triggerAlarmResultDTO : allAlarmResultDTOList) {
                        // 监控数据
                        MonitorGameDay monitorGameDay = triggerAlarmResultDTO.getMonitorGameDay();
                        ConditionDetailDTO conditionDetailDTO = triggerAlarmResultDTO.getConditionDetailDTO();
                        String dataId = monitorGameDay.getId();
                        Integer dataType = monitorGameDay.getDataType();

                        ThresholdLog thresholdLogEntity = new ThresholdLog();
                        thresholdLogEntity.setId(IdWorker.getIdStr());
                        thresholdLogEntity.setDay(day);
                        thresholdLogEntity.setHour(hour);
                        thresholdLogEntity.setAlarmId(alarmId);
                        thresholdLogEntity.setAlarmType(alarmType);
                        thresholdLogEntity.setMpId(mpId);
                        thresholdLogEntity.setDataId(dataId);
                        thresholdLogEntity.setConditionId(conditionDetailDTO.getConditionId());
                        thresholdLogEntity.setMonitorValueRelative(triggerAlarmResultDTO.getMonitorValueRelative());
                        thresholdLogEntity.setMonitorValueAbsolute(triggerAlarmResultDTO.getMonitorValueAbsolute());
                        thresholdLogEntity.setMonitorValueOldAbsolute(triggerAlarmResultDTO.getMonitorValueOldAbsolute());
                        thresholdLogEntity.setGeThresholdValueAbsolute(triggerAlarmResultDTO.getGeThresholdValueAbsolute());
                        thresholdLogEntity.setLeThresholdValueAbsolute(triggerAlarmResultDTO.getLeThresholdValueAbsolute());
                        thresholdLogEntity.setGeValue(conditionDetailDTO.getGeValue());
                        thresholdLogEntity.setLeValue(conditionDetailDTO.getLeValue());
                        thresholdLogEntity.setMinValue(conditionDetailDTO.getMinValue());
                        thresholdLogEntity.setMaxValue(conditionDetailDTO.getMaxValue());
                        thresholdLogEntity.setValueType(conditionDetailDTO.getValueType());
                        thresholdLogEntity.setAlarm(BooleanUtils.toString(triggerAlarmResultDTO.getTriggerResult(), "1", "0", "0"));
                        thresholdLogEntity.setForceAlarm(BooleanUtils.toString(triggerAlarmResultDTO.getForceAlarm(), "1", "0", "0"));
                        thresholdLogEntity.setDataType(StrUtil.toStringOrNull(dataType));
                        thresholdLogEntity.setDt(DateUtil.parse(thresholdLogEntity.getDay()).offsetNew(DateField.HOUR_OF_DAY, thresholdLogEntity.getHour()).toJdkDate());
                        thresholdLogEntity.setCreateTime(new Date());
                        thresholdLogEntity.setStatus("1");
                        CollectionUtils.addIgnoreNull(thresholdLogEntityList, thresholdLogEntity);
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(thresholdLogEntityList)) {
                this.saveOrUpdateBatch(thresholdLogEntityList);
            }
        }
        return templateDataList;
    }

    @Override
    public List<TemplateData> listMonitorDayTemplateData(String day) {
        // 已修正
        List<TemplateData> allTemplateDataList = new ArrayList<>();

        // 数据无效
        Boolean notValidData = Boolean.TRUE;
        Boolean isDayDataAbMps = mpsService.isDayDataAb(day);
        Boolean isDayTagAb = mpsService.isDayTagAb(day);
        // MPS回传数据和标签数据均为正常时该数据才有效
        if ((!isDayDataAbMps) && (!isDayTagAb)) {
            notValidData = Boolean.FALSE;
        }

        notValidData = Boolean.FALSE;

        // 未修正
        // 统计周期(=1最近1小时,=2最近一天,=3最近一天(覆盖所有场地),=-1最近1小时(数据校验),=-2最近一天(数据校验))
        List<TemplateData> templateDataList = this.listMonitorTemplateData(day, "2");
        // 数据修正：1.风控标签数据异常不发送告警；2.对应条件低于最低值不发送告警；
        if (CollectionUtils.isNotEmpty(templateDataList)) {
            for (TemplateData templateData : templateDataList) {
                // 1.风控标签数据异常不发送告警
                templateData.setNotValidData(notValidData);
                // 2.对应条件低于最低值不发送告警
                // 该条告警信息的触发结果
                List<TriggerAlarmResultDTO> allAlarmResultDTOList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(templateData.getTriggerAlarmResultDTOList())) {
                    CollectionUtils.addAll(allAlarmResultDTOList, templateData.getTriggerAlarmResultDTOList());
                }
                if (CollectionUtils.isNotEmpty(templateData.getNotTriggerAlarmResultDTOList())) {
                    CollectionUtils.addAll(allAlarmResultDTOList, templateData.getNotTriggerAlarmResultDTOList());
                }

                // (修正后)该条告警信息的触发结果(触发告警)
                List<TriggerAlarmResultDTO> newTriggerAlarmResultDTOList = new ArrayList<>();
                // (修正后)该条告警信息的触发结果(未触发告警)
                List<TriggerAlarmResultDTO> newNotTriggerAlarmResultDTOList = new ArrayList<>();

                // 修正数据
                if (CollectionUtils.isNotEmpty(allAlarmResultDTOList)) {
                    // 是否触发告警
                    Boolean isAlarm = Boolean.FALSE;
                    for (TriggerAlarmResultDTO triggerAlarmResultDTO : allAlarmResultDTOList) {
                        MonitorGameDay monitorGameDay = triggerAlarmResultDTO.getMonitorGameDay();
                        // 校验ge,用户数低于100不告警
                        if (triggerAlarmResultDTO.getGeThresholdValueAbsolute() != null) {
                            if (monitorGameDay.getUidCnt() == null || Long.valueOf(100).compareTo(monitorGameDay.getUidCnt()) > 0) {
                                triggerAlarmResultDTO.setGeThresholdValueAbsolute(BigDecimal.valueOf(100));
                                triggerAlarmResultDTO.setTriggerResult(Boolean.FALSE);
                            }
                        }
                        if (triggerAlarmResultDTO.getTriggerResult()) {
                            isAlarm = Boolean.TRUE;
                            CollectionUtils.addIgnoreNull(newTriggerAlarmResultDTOList, triggerAlarmResultDTO);
                        } else {
                            CollectionUtils.addIgnoreNull(newNotTriggerAlarmResultDTOList, triggerAlarmResultDTO);
                        }
                    }
                    templateData.setIsAlarm(isAlarm);
                    templateData.setTriggerAlarmResultDTOList(newTriggerAlarmResultDTOList);
                    templateData.setNotTriggerAlarmResultDTOList(newNotTriggerAlarmResultDTOList);
                }

            }
            allTemplateDataList.addAll(templateDataList);
        }

        return allTemplateDataList;
    }

    @Override
    public Boolean delete(String alarmId, String day, Integer hour) {
        if (StringUtils.isNotBlank(alarmId)) {
            QueryWrapper<ThresholdLog> thresholdLogQueryWrapper = new QueryWrapper<>();
            thresholdLogQueryWrapper.lambda().eq(StringUtils.isNotBlank(alarmId), ThresholdLog::getAlarmId, alarmId);
            thresholdLogQueryWrapper.lambda().eq(StringUtils.isNotBlank(day), ThresholdLog::getDay, day);
            thresholdLogQueryWrapper.lambda().eq(hour != null, ThresholdLog::getHour, hour);
            this.remove(thresholdLogQueryWrapper);
        }
        return Boolean.TRUE;
    }

    private List<TemplateData> listMonitorTemplateData(String dayHour, String alarmType) {
        Date date;
        String timeStart;
        String timeEnd;
        // 根据统计周期设置时间类型
        // 统计周期(=1最近1小时,=2最近一天,=3最近一天(覆盖所有场地),=-1最近1小时(数据校验),=-2最近一天(数据校验))
        // 时间类型(=0小时,=1天)
        Integer timeType;
        if (StringUtils.equalsIgnoreCase("1", alarmType) || StringUtils.equalsIgnoreCase("-1", alarmType)) {
            timeType = 0;
            date = StringUtils.isBlank(dayHour) ? DateUtil.beginOfHour(DateUtil.offsetHour(new Date(), -1)).toJdkDate() : DateUtil.beginOfHour(DateUtil.parse(dayHour)).toJdkDate();
            timeStart = DateUtil.beginOfHour(date).toString(DatePattern.NORM_DATETIME_PATTERN);
            timeEnd = DateUtil.endOfHour(date).toString(DatePattern.NORM_DATETIME_PATTERN);
        } else if (StringUtils.equalsIgnoreCase("2", alarmType) || StringUtils.equalsIgnoreCase("-2", alarmType)) {
            timeType = 1;
            date = StringUtils.isBlank(dayHour) ? DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), -1)).toJdkDate() : DateUtil.beginOfDay(DateUtil.parse(dayHour)).toJdkDate();
            timeStart = DateUtil.beginOfDay(date).toString(DatePattern.NORM_DATETIME_PATTERN);
            timeEnd = DateUtil.endOfDay(date).toString(DatePattern.NORM_DATETIME_PATTERN);
        } else {
            log.warn("告警统计周期不合法，不执行监控告警,统计周期=[{}](=1最近1小时,=2最近一天,=3最近一天(覆盖所有场地),=-1最近1小时(数据校验),=-2最近一天(数据校验))！", alarmType);
            return null;
        }

        String day = DateUtil.format(date, DatePattern.NORM_DATE_PATTERN);
        Integer hour = DateUtil.hour(date, Boolean.TRUE);

        // 告警信息及告警条件详情
        // 统计周期(=1最近1小时,=2最近一天,=3最近一天(覆盖所有场地),=-1最近1小时(数据校验),=-2最近一天(数据校验))
        List<AlarmDetailDTO> alarmDetailDTOList = alarmService.listAlarmDetail(alarmType, "1", "1", "1");
        if (CollectionUtils.isEmpty(alarmDetailDTOList)) {
            log.warn("告警条件为空，不执行监控告警,统计周期=[{}](=1最近1小时,=2最近一天,=3最近一天(覆盖所有场地),=-1最近1小时(数据校验),=-2最近一天(数据校验))！", alarmType);
            return null;
        }
        // 告警结果
        List<TemplateData> templateDataList = new ArrayList<>();

        // 遍历告警信息及告警条件详情
        for (AlarmDetailDTO alarmDetailDTO : alarmDetailDTOList) {
            // 是否触发告警
            Boolean isAlarm = Boolean.FALSE;
            Long mpId = alarmDetailDTO.getMpId();
            Map<String, MonitorGameDay> idAndMonitorGameDayMap = monitorGameDayService.listMonitorGameDayMap(day, hour, new HashSet<>(Arrays.asList(mpId)), timeType, null, 1);

            // TODO 统计数据类型和domainNameEn从告警基本信息表中获取
            Integer dataType = 0;
            String domainNameEn = CommonConstant.DEFAULT_DOMAIN_NAME_EN;
            List<ConditionDetailDTO> conditionDetailDTOList = alarmDetailDTO.getConditionDetailDTOList();
            // 遍历该告警的所有告警条件详情
            if (CollectionUtils.isNotEmpty(conditionDetailDTOList)) {
                // 该条告警信息的触发结果(触发告警)
                List<TriggerAlarmResultDTO> triggerAlarmResultDTOList = new ArrayList<>();
                // 该条告警信息的触发结果(未触发告警)
                List<TriggerAlarmResultDTO> notTriggerAlarmResultDTOList = new ArrayList<>();
                for (ConditionDetailDTO conditionDetailDTO : conditionDetailDTOList) {
                    String conditionContent = StringUtils.defaultIfBlank(conditionDetailDTO.getConditionContent(), "NULL");
                    String id = MonitorDataUtils.generateMonitorGameDayId(timeType, dataType, day, hour, domainNameEn, conditionContent, mpId);
                    MonitorGameDay monitorGameDay = idAndMonitorGameDayMap.get(id);
                    // TODO 为空要补充空白数据，不存在参赛情况数据也要补充数据
                    if (monitorGameDay == null || monitorGameDay.getUidCnt() == null) {
                        log.warn("无法获取到监控值或监控值为空!可能接收到不支持的条件类型,条件=[{}],需要的数据ID=[{}],获取到的数据=[{}]", conditionContent, id, JSONObject.toJSONString(monitorGameDay));
                        continue;
                    }

                    // 监控的值
                    BigDecimal monitorValue = BigDecimal.valueOf(monitorGameDay.getUidCnt());
                    TriggerAlarmResultDTO triggerAlarmResultDTO = this.isTriggerMonitorHourAlarm(dataType, day, hour, mpId, conditionDetailDTO, monitorValue);
                    triggerAlarmResultDTO.setMonitorGameDay(monitorGameDay);
                    // 是否触发告警
                    if (BooleanUtils.toBooleanDefaultIfNull(triggerAlarmResultDTO.getTriggerResult(), Boolean.FALSE)) {
                        isAlarm = Boolean.TRUE;
                        CollectionUtils.addIgnoreNull(triggerAlarmResultDTOList, triggerAlarmResultDTO);
                    } else {
                        CollectionUtils.addIgnoreNull(notTriggerAlarmResultDTOList, triggerAlarmResultDTO);
                    }
                }
                TemplateData templateData = new TemplateData();
                templateData.setIsAlarm(isAlarm);
                templateData.setDataType(dataType);
                templateData.setDay(day);
                templateData.setHour(hour);
                templateData.setTimeStart(timeStart);
                templateData.setTimeEnd(timeEnd);
                templateData.setAlarmDetailDTO(alarmDetailDTO);
                templateData.setTriggerAlarmResultDTOList(triggerAlarmResultDTOList);
                templateData.setNotTriggerAlarmResultDTOList(notTriggerAlarmResultDTOList);
                templateData.setCreateTime(DateUtil.formatDateTime(new Date()));
                CollectionUtils.addIgnoreNull(templateDataList, templateData);
            }
        }

        return templateDataList;
    }

    private TriggerAlarmResultDTO isTriggerMonitorHourAlarm(Integer dataType, String day, Integer hour, Long mpId, ConditionDetailDTO conditionDetailDTO, BigDecimal monitorValue) {
        TriggerAlarmResultDTO triggerAlarmResultDTO = new TriggerAlarmResultDTO();
        // 触发的告警条件详情
        triggerAlarmResultDTO.setConditionDetailDTO(conditionDetailDTO);
        // 监控的值绝对值
        triggerAlarmResultDTO.setMonitorValueAbsolute(monitorValue);
        String conditionContent = StringUtils.defaultIfBlank(conditionDetailDTO.getConditionContent(), "NULL");
        // 条件值
        // 大于等于>=
        BigDecimal geValue = conditionDetailDTO.getGeValue();
        // 小于等于<=
        BigDecimal leValue = conditionDetailDTO.getLeValue();
        // 基线，最小值，低于该值触发告警
        BigDecimal minValue = conditionDetailDTO.getMinValue();
        // 基线，最大值，超过该值触发告警
        BigDecimal maxValue = conditionDetailDTO.getMaxValue();
        // 条件值类型
        String valueType = conditionDetailDTO.getValueType();
        // 是否触发告警
        Boolean triggerResult;
        // 1.判断是否触发基线(强制告警)
        triggerResult = (maxValue != null && monitorValue.compareTo(maxValue) > 0) || (minValue != null && monitorValue.compareTo(minValue) < 0);
        triggerAlarmResultDTO.setTriggerResult(triggerResult);
        if (triggerResult) {
            // 是否为强制告警
            triggerAlarmResultDTO.setForceAlarm(Boolean.TRUE);
            // 监控值相对值
            triggerAlarmResultDTO.setMonitorValueRelative(BigDecimal.ZERO);
            triggerAlarmResultDTO.setMonitorValueOldAbsolute(BigDecimal.ZERO);
            // 阈值绝对值
            triggerAlarmResultDTO.setGeThresholdValueAbsolute(maxValue);
            triggerAlarmResultDTO.setLeThresholdValueAbsolute(minValue);
            return triggerAlarmResultDTO;
        }
        // 是否为强制告警
        triggerAlarmResultDTO.setForceAlarm(Boolean.FALSE);

        // 2.判断是否为自定义值类型告警(绝对值)
        // =1自定义值
        if (StringUtils.equalsIgnoreCase(AlarmConditionValueTypeEnum.CUSTOM_VALUE.getValue(), valueType)) {
            triggerResult = (geValue != null && monitorValue.compareTo(geValue) >= 0) || (leValue != null && monitorValue.compareTo(leValue) <= 0);
            triggerAlarmResultDTO.setTriggerResult(triggerResult);
            // 监控值绝对值
            triggerAlarmResultDTO.setMonitorValueRelative(BigDecimal.ZERO);
            triggerAlarmResultDTO.setMonitorValueOldAbsolute(BigDecimal.ZERO);
            // 阈值绝对值
            triggerAlarmResultDTO.setGeThresholdValueAbsolute(geValue);
            triggerAlarmResultDTO.setLeThresholdValueAbsolute(leValue);
            return triggerAlarmResultDTO;
        }

        // 不为自定义值则需要通过历史数据计算阈值
        List<MonitorGameDay> monitorGameDayList = this.listHistoricalMonitorGameDayData(conditionContent, dataType, day, hour, mpId, valueType);
        // 根据历史数据计算相关参数
        // 是否去掉最高和最低
        boolean removeMaxMin = StringUtils.equalsAnyIgnoreCase(valueType, AlarmConditionValueTypeEnum.DAY_7_DAY_REMOVE_MAX_MIN_AVG_VALUE.getValue(), AlarmConditionValueTypeEnum.DAY_7_DAY_REMOVE_MAX_MIN_AVG_RATE.getValue(), AlarmConditionValueTypeEnum.DAY_7_DAY_REMOVE_MAX_MIN_1_STANDARD_RATE.getValue(), AlarmConditionValueTypeEnum.DAY_7_DAY_REMOVE_MAX_MIN_2_STANDARD_RATE.getValue(), AlarmConditionValueTypeEnum.DAY_7_DAY_REMOVE_MAX_MIN_3_STANDARD_RATE.getValue());
        CalculateHistoricalDataDTO calculateHistoricalDataDTO = this.getCalculateHistoricalDataDTO(conditionContent, monitorGameDayList, removeMaxMin);
        // TODO 不存在最近7日历史数据怎么处理？
        if (CollectionUtils.isEmpty(monitorGameDayList) || CollectionUtils.isEmpty(calculateHistoricalDataDTO.getDoubleList())) {
            log.warn("不存在最近7日历史数据，无法根据历史数据计算，默认为不触发告警!时间=[{} {}:00:00],条件=[{}],比赛产品=[{}],数据类型=[{}]", day, hour, conditionContent, mpId, dataType);
            // 不存在最近7日历史数据，无法根据历史数据计算，默认为不触发告警
            triggerResult = Boolean.FALSE;
            triggerAlarmResultDTO.setTriggerResult(triggerResult);
            // 监控值相对值
            triggerAlarmResultDTO.setMonitorValueRelative(BigDecimal.ZERO);
            triggerAlarmResultDTO.setMonitorValueOldAbsolute(BigDecimal.ZERO);
            // 阈值绝对值
            if (geValue != null) {
//                triggerAlarmResultDTO.setGeThresholdValueAbsolute((maxValue != null) ? maxValue : triggerAlarmResultDTO.getMonitorValueAbsolute());
                triggerAlarmResultDTO.setGeThresholdValueAbsolute(triggerAlarmResultDTO.getMonitorValueAbsolute());
            }
            if (leValue != null) {
//                triggerAlarmResultDTO.setLeThresholdValueAbsolute((minValue != null) ? minValue : triggerAlarmResultDTO.getMonitorValueAbsolute());
                triggerAlarmResultDTO.setLeThresholdValueAbsolute(triggerAlarmResultDTO.getMonitorValueAbsolute());
            }
            return triggerAlarmResultDTO;
        }
        // 历史数据
        long size = calculateHistoricalDataDTO.getSize();
        double sum = calculateHistoricalDataDTO.getSum();
        double max = calculateHistoricalDataDTO.getMax();
        double min = calculateHistoricalDataDTO.getMin();
        double average = calculateHistoricalDataDTO.getAverage();
        // 总体方差
        double populationVariance = calculateHistoricalDataDTO.getPopulationVariance();
        // 总体标准差(方差开方)
        double populationStandardDeviation = calculateHistoricalDataDTO.getPopulationStandardDeviation();
        double yesterdayMonitorValue = calculateHistoricalDataDTO.getYesterdayMonitorValue();

        // =2最近7日平均值
        // =3最近7日去掉最高和最低平均值
        if (StringUtils.equalsAnyIgnoreCase(valueType, AlarmConditionValueTypeEnum.DAY_7_DAY_AVG_VALUE.getValue(), AlarmConditionValueTypeEnum.DAY_7_DAY_REMOVE_MAX_MIN_AVG_VALUE.getValue())) {
            if (geValue != null) {
                geValue = BigDecimal.valueOf(average);
            }
            if (leValue != null) {
                leValue = BigDecimal.valueOf(average);
            }
            triggerResult = (geValue != null && monitorValue.compareTo(geValue) >= 0) || (leValue != null && monitorValue.compareTo(leValue) <= 0);
            triggerAlarmResultDTO.setTriggerResult(triggerResult);
            // 监控值绝对值
            triggerAlarmResultDTO.setMonitorValueRelative(monitorValue);
            triggerAlarmResultDTO.setMonitorValueOldAbsolute(BigDecimal.valueOf(average));
            // 阈值绝对值
            triggerAlarmResultDTO.setGeThresholdValueAbsolute(geValue);
            triggerAlarmResultDTO.setLeThresholdValueAbsolute(leValue);
        } else if (StringUtils.equalsIgnoreCase(AlarmConditionValueTypeEnum.DAY_YESTERDAY_RATE.getValue(), valueType)) {
            // =4相对前一天变化率
            // 相对的旧值绝对值(前一天绝对值)
            BigDecimal monitorValueOldAbsolute = BigDecimal.valueOf(yesterdayMonitorValue);
            if (monitorValueOldAbsolute.compareTo(BigDecimal.ZERO) != 0) {
                // 监控的相对值
                monitorValue = monitorValue.subtract(monitorValueOldAbsolute).divide(monitorValueOldAbsolute, 4, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100.00));

                triggerResult = (geValue != null && monitorValue.compareTo(geValue) >= 0) || (leValue != null && monitorValue.compareTo(leValue) <= 0);
                triggerAlarmResultDTO.setTriggerResult(triggerResult);
                // 监控的相对值
                triggerAlarmResultDTO.setMonitorValueRelative(monitorValue);
                triggerAlarmResultDTO.setMonitorValueOldAbsolute(monitorValueOldAbsolute);
                // 阈值绝对值
                if (geValue != null) {
                    triggerAlarmResultDTO.setGeThresholdValueAbsolute(monitorValueOldAbsolute.multiply(geValue.divide(BigDecimal.valueOf(100.00), 4, BigDecimal.ROUND_HALF_UP).add(BigDecimal.valueOf(1))));
                }
                if (leValue != null) {
                    triggerAlarmResultDTO.setLeThresholdValueAbsolute(monitorValueOldAbsolute.multiply(leValue.divide(BigDecimal.valueOf(100.00), 4, BigDecimal.ROUND_HALF_UP).add(BigDecimal.valueOf(1))));
                }
            }

        } else if (StringUtils.equalsAnyIgnoreCase(valueType, AlarmConditionValueTypeEnum.DAY_7_DAY_AVG_RATE.getValue(), AlarmConditionValueTypeEnum.DAY_7_DAY_REMOVE_MAX_MIN_AVG_RATE.getValue())) {
            // =5相对最近7日平均值变化率
            // =6相对最近7日去掉最高和最低平均值变化率
            // 相对的旧值绝对值
            BigDecimal monitorValueOldAbsolute = BigDecimal.valueOf(average);
            if (monitorValueOldAbsolute.compareTo(BigDecimal.ZERO) != 0) {
                // 监控的相对值
                monitorValue = monitorValue.subtract(monitorValueOldAbsolute).divide(monitorValueOldAbsolute, 4, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100.00));

                triggerResult = (geValue != null && monitorValue.compareTo(geValue) >= 0) || (leValue != null && monitorValue.compareTo(leValue) <= 0);
                triggerAlarmResultDTO.setTriggerResult(triggerResult);
                // 监控的相对值
                triggerAlarmResultDTO.setMonitorValueRelative(monitorValue);
                triggerAlarmResultDTO.setMonitorValueOldAbsolute(monitorValueOldAbsolute);
                // 阈值绝对值
                if (geValue != null) {
                    triggerAlarmResultDTO.setGeThresholdValueAbsolute(monitorValueOldAbsolute.multiply(geValue.divide(BigDecimal.valueOf(100.00), 4, BigDecimal.ROUND_HALF_UP).add(BigDecimal.valueOf(1))).setScale(0, RoundingMode.HALF_UP));
                }
                if (leValue != null) {
                    triggerAlarmResultDTO.setLeThresholdValueAbsolute(monitorValueOldAbsolute.multiply(leValue.divide(BigDecimal.valueOf(100.00), 4, BigDecimal.ROUND_HALF_UP).add(BigDecimal.valueOf(1))).setScale(0, RoundingMode.HALF_UP));
                }
            }
        } else if (StringUtils.equalsAnyIgnoreCase(valueType, AlarmConditionValueTypeEnum.DAY_7_DAY_REMOVE_MAX_MIN_1_STANDARD_RATE.getValue(), AlarmConditionValueTypeEnum.DAY_7_DAY_REMOVE_MAX_MIN_2_STANDARD_RATE.getValue(), AlarmConditionValueTypeEnum.DAY_7_DAY_REMOVE_MAX_MIN_3_STANDARD_RATE.getValue())) {
            // =7相对最近7日去掉最高和最低正态分布1个标准差σ的变化率(正态分布1σ-->68%)
            // =8相对最近7日去掉最高和最低正态分布2个标准差σ的变化率(正态分布2σ-->95%)
            // =9相对最近7日去掉最高和最低正态分布3个标准差σ的变化率(正态分布3σ-->99%)
            long multiply = 3;
            if (StringUtils.equalsIgnoreCase(AlarmConditionValueTypeEnum.DAY_7_DAY_REMOVE_MAX_MIN_1_STANDARD_RATE.getValue(), valueType)) {
                multiply = 1;
            } else if (StringUtils.equalsIgnoreCase(AlarmConditionValueTypeEnum.DAY_7_DAY_REMOVE_MAX_MIN_2_STANDARD_RATE.getValue(), valueType)) {
                multiply = 2;
            }
            // 相对的旧值绝对值
            BigDecimal monitorValueOldAbsolute = BigDecimal.valueOf(average).add(BigDecimal.valueOf(populationStandardDeviation).multiply(BigDecimal.valueOf(multiply)));
            if (monitorValueOldAbsolute.compareTo(BigDecimal.ZERO) != 0) {
                // 监控的相对值
                monitorValue = monitorValue.subtract(monitorValueOldAbsolute).divide(monitorValueOldAbsolute, 4, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100.00));

                triggerResult = (geValue != null && monitorValue.compareTo(geValue) >= 0) || (leValue != null && monitorValue.compareTo(leValue) <= 0);
                triggerAlarmResultDTO.setTriggerResult(triggerResult);
                // 监控的相对值
                triggerAlarmResultDTO.setMonitorValueRelative(monitorValue);
                triggerAlarmResultDTO.setMonitorValueOldAbsolute(monitorValueOldAbsolute);
                // 阈值绝对值
                if (geValue != null) {
                    triggerAlarmResultDTO.setGeThresholdValueAbsolute(monitorValueOldAbsolute.multiply(geValue.divide(BigDecimal.valueOf(100.00), 4, BigDecimal.ROUND_HALF_UP).add(BigDecimal.valueOf(1))).setScale(0, RoundingMode.HALF_UP));
                }
                if (leValue != null) {
                    triggerAlarmResultDTO.setLeThresholdValueAbsolute(monitorValueOldAbsolute.multiply(leValue.divide(BigDecimal.valueOf(100.00), 4, BigDecimal.ROUND_HALF_UP).add(BigDecimal.valueOf(1))).setScale(0, RoundingMode.HALF_UP));
                }
            }
        } else {
            log.warn("不支持的阈值类型[{}]", valueType);
        }
        log.debug("判断触发结果[{}],阈值类型[{}],比赛产品ID=[{}],条件=[{}],geValue=[{}],leValue=[{}],监控值=[{}]", JSONObject.toJSONString(triggerAlarmResultDTO), valueType, mpId, conditionContent, geValue, leValue, monitorValue);
        return triggerAlarmResultDTO;
    }

    /**
     * @param tagNameEn 标签英文名
     * @param dataType  统计数据类型
     * @param day       日期
     * @param hour      小时
     * @param mpId      比赛产品ID
     * @param valueType 阈值类型
     * @return 监控数据(小时)
     */
    private List<MonitorGameDay> listHistoricalMonitorGameDayData(String tagNameEn, Integer dataType, String day, Integer hour, Long mpId, String valueType) {

        String getKey = String.format("%s_%s_%s_%s_%s_%s", tagNameEn, dataType, day, hour, mpId, valueType);
        // 从缓存中取
        Object cacheValue = commonCacheOperator.get(HISTORICAL_MONITOR_GAME_DAY_DATA_CACHE_KEY + getKey);
        if (ObjectUtil.isNotEmpty(cacheValue)) {
            log.info("从缓存获取成功,getKey={}",getKey);
            return JSONObject.parseArray(Convert.toStr(cacheValue), MonitorGameDay.class);
        }

        // 若缓存中未获取到则更新缓存
        List<MonitorGameDay> monitorGameDayList = null;
        if (StringUtils.equalsAnyIgnoreCase(valueType, AlarmConditionValueTypeEnum.CUSTOM_VALUE.getValue(), AlarmConditionValueTypeEnum.DAY_7_DAY_AVG_VALUE.getValue(), AlarmConditionValueTypeEnum.DAY_7_DAY_REMOVE_MAX_MIN_AVG_VALUE.getValue(), AlarmConditionValueTypeEnum.DAY_YESTERDAY_RATE.getValue(), AlarmConditionValueTypeEnum.DAY_7_DAY_AVG_RATE.getValue(), AlarmConditionValueTypeEnum.DAY_7_DAY_REMOVE_MAX_MIN_AVG_RATE.getValue(), AlarmConditionValueTypeEnum.DAY_7_DAY_REMOVE_MAX_MIN_1_STANDARD_RATE.getValue(), AlarmConditionValueTypeEnum.DAY_7_DAY_REMOVE_MAX_MIN_2_STANDARD_RATE.getValue(), AlarmConditionValueTypeEnum.DAY_7_DAY_REMOVE_MAX_MIN_3_STANDARD_RATE.getValue())) {
            // 时间类型(=0小时,=1天)
            Integer timeType = 1;
            QueryWrapper<MonitorGameDay> monitorGameDayQueryWrapper = new QueryWrapper<>();
            // 最近7日(需要排除昨日,因此需要在指定日期之前-1)
            monitorGameDayQueryWrapper.lambda().between(MonitorGameDay::getDay, DateUtil.format(DateUtil.offsetDay(DateUtil.parse(day), -7), DatePattern.NORM_DATE_PATTERN), DateUtil.format(DateUtil.offsetDay(DateUtil.parse(day), -1), DatePattern.NORM_DATE_PATTERN));
//            monitorGameDayQueryWrapper.lambda().eq(StringUtils.isNotBlank(tagNameEn), MonitorGameDay::getTagNameEn, tagNameEn);
            monitorGameDayQueryWrapper.lambda().eq(mpId != null, MonitorGameDay::getMpId, mpId);
            monitorGameDayQueryWrapper.lambda().eq(MonitorGameDay::getTimeType, timeType);
            monitorGameDayQueryWrapper.lambda().eq(dataType != null, MonitorGameDay::getDataType, dataType);
            monitorGameDayQueryWrapper.lambda().eq(MonitorGameDay::getStatus, 1);
            monitorGameDayQueryWrapper.lambda().orderByDesc(MonitorGameDay::getDt);
            monitorGameDayList = monitorGameDayService.list(monitorGameDayQueryWrapper);
        }
        Map<String, List<MonitorGameDay>> map = new LinkedHashMap<>();
        if (CollectionUtils.isNotEmpty(monitorGameDayList)) {
            for (MonitorGameDay monitorGameDay : monitorGameDayList) {
                String putKey = String.format("%s_%s_%s_%s_%s_%s", monitorGameDay.getTagNameEn(), dataType, day, hour, mpId, valueType);
                List<MonitorGameDay> monitorGameDayListTmp = map.getOrDefault(putKey, new ArrayList<>());
                CollectionUtils.addIgnoreNull(monitorGameDayListTmp, monitorGameDay);
                map.put(putKey, monitorGameDayListTmp);
            }
            if (MapUtils.isNotEmpty(map)) {
                for (String putKey : map.keySet()) {
                    // 更新到缓存
                    commonCacheOperator.put(HISTORICAL_MONITOR_GAME_DAY_DATA_CACHE_KEY + putKey, JSONObject.toJSONString(map.get(putKey)), 600);
                }
            }
        }
        log.info("从缓存获取失败,已查询数据并放入缓存,getKey={}",getKey);
        return map.get(getKey);
    }

    private CalculateHistoricalDataDTO getCalculateHistoricalDataDTO(String conditionContent, List<MonitorGameDay> monitorGameDayList, boolean removeMaxMin) {
        CalculateHistoricalDataDTO calculateHistoricalDataDTO = new CalculateHistoricalDataDTO();

        // 根据历史数据求平均值等数据
        long size = 0;
        double sum = 0.0;
        double max = 0.0;
        double min = 0.0;
        double average = 0.0;
        // 总体方差
        double populationVariance = 0.0;
        // 总体标准差(方差开方)
        double populationStandardDeviation = 0.0;
        double yesterdayMonitorValue = 0.0;

        if (CollectionUtils.isNotEmpty(monitorGameDayList)) {
            List<Double> doubleList = MonitorDataUtils.listMonitorValue(conditionContent, monitorGameDayList);
            // 去掉一个最大值和一个最小值
            List<Double> doubleListNew = new ArrayList<>();
            if (removeMaxMin && CollectionUtils.size(doubleList) > 2) {
                int indexOfMin = doubleList.indexOf(Collections.min(doubleList));
                int indexOfMax = doubleList.indexOf(Collections.max(doubleList));
                for (int i = 0; i < CollectionUtils.size(doubleList); i++) {
                    if (i != indexOfMin && i != indexOfMax) {
                        CollectionUtils.addIgnoreNull(doubleListNew, doubleList.get(i));
                    }
                }
            } else {
                doubleListNew = doubleList;
            }
            if (CollectionUtils.isNotEmpty(doubleListNew)) {
                double[] doubleArray = doubleListNew.stream().mapToDouble(Double::doubleValue).toArray();
                size = doubleArray.length;
                sum = StatUtils.sum(doubleArray);
                max = StatUtils.max(doubleArray);
                min = StatUtils.min(doubleArray);
                average = StatUtils.mean(doubleArray);
                // 总体方差
                populationVariance = StatUtils.populationVariance(doubleArray);
                // 总体标准差(方差开方)
                populationStandardDeviation = FastMath.sqrt(StatUtils.populationVariance(doubleArray));
                calculateHistoricalDataDTO.setDoubleList(doubleListNew);
            }
            if (CollectionUtils.isNotEmpty(doubleList)) {
                yesterdayMonitorValue = doubleList.get(0);
            }
        }
        calculateHistoricalDataDTO.setSize(size);
        calculateHistoricalDataDTO.setSum(sum);
        calculateHistoricalDataDTO.setMax(max);
        calculateHistoricalDataDTO.setMin(min);
        calculateHistoricalDataDTO.setAverage(average);
        calculateHistoricalDataDTO.setPopulationVariance(populationVariance);
        calculateHistoricalDataDTO.setPopulationStandardDeviation(populationStandardDeviation);
        calculateHistoricalDataDTO.setYesterdayMonitorValue(yesterdayMonitorValue);

        return calculateHistoricalDataDTO;
    }
}
