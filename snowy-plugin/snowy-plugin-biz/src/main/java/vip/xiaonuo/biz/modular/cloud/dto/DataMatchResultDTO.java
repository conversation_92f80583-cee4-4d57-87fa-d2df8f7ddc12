package vip.xiaonuo.biz.modular.cloud.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * 数据匹配结果数据结构
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/10/11 16:34
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DataMatchResultDTO implements Serializable {

    private static final Long serialVersionUID = 1L;

    /**
     * 输入数据
     */
//    private DataSourceDTO dataSourceDTO;

    /**
     * 事件时间
     */
    private Long eventTime;

    /**
     * sessionId
     */
    private String sessionId;

    /**
     * 用户ID
     */
    private Set<Long> userIds;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * app_id
     */
    private Integer appId;

    /**
     * SDK版本号
     */
    private String sdkVersion;

    /**
     * jjDeviceId
     */
    private String jjDeviceId;

    /**
     * 最后更新时间(处理时间)
     */
    private Long lastUpdateTime;

    /**
     * 触发规则结果
     */
    private List<RuleMatchResultDTO> ruleMatchResultDTOList;
}
