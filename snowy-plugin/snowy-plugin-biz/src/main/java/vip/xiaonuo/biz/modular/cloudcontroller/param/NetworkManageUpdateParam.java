package vip.xiaonuo.biz.modular.cloudcontroller.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * Description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/11/14 16:19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "网络终端管理-策略关系状态变更(只修改状态使用)传输对象")
public class NetworkManageUpdateParam implements Serializable {

    private static final Long serialVersionUID = 1L;
    /**
     * 电脑ID
     */
    @ApiModelProperty(value = "电脑ID", required = true, position = 1)
    @NotBlank(message = "电脑ID不能为空")
    private String pcId;

    /**
     * 规则ID
     */
    @ApiModelProperty(value = "规则ID", required = true, position = 1)
    @NotBlank(message = "规则ID不能为空")
    private String ruleId;

    /**
     * 策略下发状态,1:新增成功 2:新增失败 3:新增项已添加 4:删除成功 5:删除失败
     */
    @ApiModelProperty(value = "策略下发状态,1:新增成功 2:新增失败 3:新增项已添加 4:删除成功 5:删除失败", required = true, position = 2)
    @NotBlank(message = "策略下发状态")
    private String status;

    /**
     * 错误信息
     */
    @ApiModelProperty(value = "错误信息", position = 2)
    private String error;
}
