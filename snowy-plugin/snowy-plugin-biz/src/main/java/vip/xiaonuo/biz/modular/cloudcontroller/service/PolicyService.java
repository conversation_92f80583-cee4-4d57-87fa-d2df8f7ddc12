/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.cloudcontroller.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.biz.modular.cloudcontroller.entity.Policy;
import vip.xiaonuo.biz.modular.cloudcontroller.param.PolicyAddParam;
import vip.xiaonuo.biz.modular.cloudcontroller.param.PolicyEditParam;
import vip.xiaonuo.biz.modular.cloudcontroller.param.PolicyIdParam;
import vip.xiaonuo.biz.modular.cloudcontroller.param.PolicyPageParam;

import java.util.List;

/**
 * 策略Service接口
 *
 * <AUTHOR>
 * @date 2023/10/10 16:46
 **/
public interface PolicyService extends IService<Policy> {

    /**
     * 获取策略分页
     *
     * <AUTHOR>
     * @date 2023/10/10 16:46
     */
    Page<Policy> page(PolicyPageParam policyPageParam);

    /**
     * 获取策略列表
     *
     * <AUTHOR>
     * @date 2023/10/10 16:46
     */
    List<Policy> listAll();
    /**
     * 获取策略列表
     *
     * <AUTHOR>
     * @date 2023/10/10 16:46
     */
    List<Policy> list(PolicyPageParam policyPageParam);
    /**
     * 获取可选策略列表
     *
     * <AUTHOR>
     * @date 2023/10/10 16:46
     */
    List<Policy> policyOptions(PolicyPageParam policyPageParam);

    /**
     * 添加策略
     *
     * <AUTHOR>
     * @date 2023/10/10 16:46
     */
    void add(PolicyAddParam policyAddParam);

    /**
     * 编辑策略
     *
     * <AUTHOR>
     * @date 2023/10/10 16:46
     */
    void edit(PolicyEditParam policyEditParam);

    /**
     * 删除策略
     *
     * <AUTHOR>
     * @date 2023/10/10 16:46
     */
    void delete(List<PolicyIdParam> policyIdParamList);

    /**
     * 获取策略详情
     *
     * <AUTHOR>
     * @date 2023/10/10 16:46
     */
    Policy detail(PolicyIdParam policyIdParam);

    /**
     * 获取策略详情
     *
     * <AUTHOR>
     * @date 2023/10/10 16:46
     **/
    Policy queryEntity(Integer id);
}
