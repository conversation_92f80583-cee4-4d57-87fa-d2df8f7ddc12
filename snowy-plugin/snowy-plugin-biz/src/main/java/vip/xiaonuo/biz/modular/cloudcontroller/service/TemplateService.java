/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.cloudcontroller.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.biz.modular.cloudcontroller.entity.Template;
import vip.xiaonuo.biz.modular.cloudcontroller.param.*;

import java.util.List;

/**
 * 模板表Service接口
 *
 * <AUTHOR>
 * @date 2024/03/06 16:40
 **/
public interface TemplateService extends IService<Template> {

    /**
     * 获取模板表分页
     *
     * <AUTHOR>
     * @date 2024/03/06 16:40
     */
    Page<Template> page(TemplatePageParam templatePageParam);

    /**
     * 获取模板表列表
     *
     * <AUTHOR>
     * @date 2024/03/06 16:40
     */
    List<Template> list(TemplatePageParam templatePageParam);

    /**
     * 添加模板表
     *
     * <AUTHOR>
     * @date 2024/03/06 16:40
     */
    void add(TemplateAddParam templateAddParam);

    /**
     * 编辑模板表
     *
     * <AUTHOR>
     * @date 2024/03/06 16:40
     */
    void edit(TemplateEditParam templateEditParam);

    /**
     * 编辑模板表
     *
     * <AUTHOR>
     * @date 2024/03/06 16:40
     */
    String preview(TemplatePreviewParam templatePreviewParam);

    /**
     * 删除模板表
     *
     * <AUTHOR>
     * @date 2024/03/06 16:40
     */
    void delete(List<TemplateIdParam> templateIdParamList);

    /**
     * 获取模板表详情
     *
     * <AUTHOR>
     * @date 2024/03/06 16:40
     */
    Template detail(TemplateIdParam templateIdParam);

    /**
     * 获取模板表详情
     *
     * <AUTHOR>
     * @date 2024/03/06 16:40
     **/
    Template queryEntity(String id);
}
