package vip.xiaonuo.biz.modular.cloudcontroller.service.impl;

import cn.hutool.core.compress.Deflate;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.RuntimeUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.symmetric.AES;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import vip.xiaonuo.biz.core.config.AppConfig;
import vip.xiaonuo.biz.core.constant.DataSourceConstant;
import vip.xiaonuo.biz.modular.cloudcontroller.constant.CloudControllerConstant;
import vip.xiaonuo.biz.modular.cloudcontroller.dto.*;
import vip.xiaonuo.biz.modular.cloudcontroller.entity.*;
import vip.xiaonuo.biz.modular.cloudcontroller.param.CloudControllerResultAddParam;
import vip.xiaonuo.biz.modular.cloudcontroller.param.PolicyRecordAddParam;
import vip.xiaonuo.biz.modular.cloudcontroller.param.PolicyRecordEditParam;
import vip.xiaonuo.biz.modular.cloudcontroller.service.*;
import vip.xiaonuo.biz.modular.freemarker.service.FreemarkerService;
import vip.xiaonuo.biz.modular.message.service.MessageService;
import vip.xiaonuo.biz.modular.remote.service.AIRemoteService;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.util.HexToByteUtil;
import vip.xiaonuo.dev.api.DevConfigApi;
import vip.xiaonuo.dev.api.DevFileApi;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * Description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/10/17 10:58
 */
@Service
@DS(DataSourceConstant.DYNAMIC_DATASOURCE_CLOUD)
@Slf4j
public class PolicyTemplateManageServiceImpl implements PolicyTemplateManageService {

    @Autowired
    private PolicyService policyService;

    @Autowired
    private PolicyClassService policyClassService;

    @Autowired
    private PolicyLimitService policyLimitService;

    @Autowired
    private PolicyRecordService policyRecordService;

    @Autowired
    private LuaTaskService luaTaskService;

    @Autowired
    private FreemarkerService freemarkerService;

    @Autowired
    private MessageService messageService;

    @Autowired
    private DevFileApi devFileApi;

    @Autowired
    private DevConfigApi devConfigApi;

    @Autowired
    private CloudControllerResultService cloudControllerResultService;

    @Autowired
    private PolicyResultLinkService policyResultLinkService;

    @Autowired
    private RecordResultLinkService recordResultLinkService;

    @Autowired
    private AIRemoteService aiRemoteService;

    @Autowired
    private NetworkManageService networkManageService;

    //    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(DataMatchResultDTO dataMatchResultDTO) {
//        log.info("接收到更新策略，dataMatchResultDTO=[{}]", JSONObject.toJSONString(dataMatchResultDTO));
        if (StringUtils.isBlank(dataMatchResultDTO.getDeviceId()) || CollectionUtils.isEmpty(dataMatchResultDTO.getRuleMatchResultDTOList())) {
            log.warn("接收到不合法数据，设备ID或者触发结果为空，将不执行策略下发,dataMatchResultDTO=[{}]", JSONObject.toJSONString(dataMatchResultDTO));
            return;
        }

        String deviceId = dataMatchResultDTO.getDeviceId();
        Integer appId = dataMatchResultDTO.getAppId();
        String sdkVersion = dataMatchResultDTO.getSdkVersion();
        // 本次是否强制下发(将不进行下发过滤)
        Boolean isForceSend = BooleanUtils.toBooleanDefaultIfNull(dataMatchResultDTO.getIsForceSend(), Boolean.FALSE);

        List<RuleMatchResultDTO> ruleMatchResultDTOList = dataMatchResultDTO.getRuleMatchResultDTOList();
        // 通过策略限制
        List<RuleMatchResultDTO> policyIds = new ArrayList<>();
        // 1.验证策略限制
        if (StringUtils.isNotBlank(deviceId) && CollectionUtils.isNotEmpty(ruleMatchResultDTOList)) {
            for (RuleMatchResultDTO ruleMatchResultDTO : ruleMatchResultDTOList) {
                // 策略限制表
                PolicyLimit policyLimit = policyLimitService.getById(ruleMatchResultDTO.getPolicyId());
                if (policyLimit != null) {
                    List<String> deviceIdList = new ArrayList<>();
                    String deviceIds = policyLimit.getDeviceIdList();
                    if (StringUtils.isNotBlank(deviceIds)) {
                        deviceIdList = JSONObject.parseArray(deviceIds, String.class);
                    }
                    Integer deviceMaxNum = policyLimit.getDeviceMaxNum();
                    // 没有超过允许最大设备数并且未包含该设备ID才下发
                    if ((deviceMaxNum.compareTo(CollectionUtils.size(deviceIdList)) > 0) && (!deviceIdList.contains(deviceId))) {
                        policyIds.add(ruleMatchResultDTO);
                    }
                } else {
                    policyIds.add(ruleMatchResultDTO);
                }
            }
        }

        // 2.验证策略下发记录
        if ((!isForceSend) && CollectionUtils.isNotEmpty(policyIds)) {
            // 策略下发记录表
            // 根据设备ID获取已下发的策略ID列表
            List<Integer> policyIdsOfDeviceId = policyRecordService.listPolicyIdByDeviceId(deviceId);
            policyIds = policyIds.stream().filter(ruleMatchResultDTO -> !policyIdsOfDeviceId.contains(ruleMatchResultDTO.getPolicyId())).collect(Collectors.toList());
        }

        // 3.参数合法性校验及合并
        Map<String, PolicyTemplateDTO> policyTemplateDTOMap = new LinkedHashMap<>();
        if (CollectionUtils.isNotEmpty(policyIds)) {
            for (RuleMatchResultDTO ruleMatchResultDTO : policyIds) {
                // 策略表
                Policy policy = policyService.getById(ruleMatchResultDTO.getPolicyId());
                if (policy != null && StringUtils.equalsIgnoreCase("1", policy.getStatus()) && policy.getPolicyClassId() != null) {
                    // 策略种类表
                    PolicyClass policyClass = policyClassService.getById(policy.getPolicyClassId());
                    // Lua任务列表
                    String luaTaskIdList = policyClass.getLuaTaskList();
                    // LuaTask表
                    // TODO 通过ID进行查询
                    List<LuaTask> luaTaskList = luaTaskService.list(new QueryWrapper<LuaTask>().lambda().in(LuaTask::getLuaTaskId, JSONObject.parseArray(luaTaskIdList, String.class)));
                    if (CollectionUtils.isNotEmpty(luaTaskList)) {
                        for (LuaTask luaTask : luaTaskList) {
                            PolicyTemplateDTO policyTemplateDTO = policyTemplateDTOMap.getOrDefault(luaTask.getLuaTaskNameEn(), new PolicyTemplateDTO());

                            policyTemplateDTO.setDeviceId(deviceId);
                            policyTemplateDTO.setLuaTask(luaTask);
                            policyTemplateDTO.setBaseConfig(luaTask.getLuaTaskNameEn());
                            // 普通参数
                            Map<String, List<Integer>> params = policyTemplateDTO.getParams();
                            if (params == null) {
                                params = new LinkedHashMap<>();
                            }
                            List<String> paramsOfLuaTask = null;
                            if (MapUtils.isNotEmpty(ruleMatchResultDTO.getParamsOfLuaTaskMap())) {
                                paramsOfLuaTask = ruleMatchResultDTO.getParamsOfLuaTaskMap().get(luaTask.getLuaTaskNameEn());
                            }
                            if (CollectionUtils.isNotEmpty(paramsOfLuaTask)) {
                                for (String param : paramsOfLuaTask) {
                                    if (StringUtils.isNotBlank(param)) {
                                        List<Integer> policyIdListOfParam = params.getOrDefault(param, new ArrayList<>());
                                        if (!policyIdListOfParam.contains(policy.getPolicyId())) {
                                            policyIdListOfParam.add(policy.getPolicyId());
                                        }
                                        params.put(param, policyIdListOfParam);
                                    }
                                }
                            }
                            policyTemplateDTO.setParams(params);

                            // 扩展参数
                            Map<String, PolicyTemplatEextendedParamsDTO> extendedParams = policyTemplateDTO.getExtendedParams();
                            if (extendedParams == null) {
                                extendedParams = new LinkedHashMap<>();
                            }
                            // 按照key排序后的扩展参数
                            List<Map<String, String>> extendedParamsOfLuaTaskList = null;
                            if (MapUtils.isNotEmpty(ruleMatchResultDTO.getExtendedParamsOfLuaTaskMap())) {
                                extendedParamsOfLuaTaskList = ruleMatchResultDTO.getExtendedParamsOfLuaTaskMap().get(luaTask.getLuaTaskNameEn());
                            }

                            if (CollectionUtils.isNotEmpty(extendedParamsOfLuaTaskList)) {
                                for (Map<String, String> paramMap : extendedParamsOfLuaTaskList) {
                                    if (MapUtils.isNotEmpty(paramMap)) {
                                        // TreeMap 会根据键的自然顺序进行排序
                                        TreeMap<String, String> sortedParamMap = new TreeMap<>(paramMap);
                                        StringBuilder paramKey = new StringBuilder();
                                        for (String paramName : sortedParamMap.keySet()) {
                                            if (StringUtils.isNotBlank(paramName) && StringUtils.isNotBlank(sortedParamMap.get(paramName))) {
                                                paramKey.append(String.format("%s===%s", paramName, sortedParamMap.get(paramName))).append(";;;");
                                            }
                                        }
                                        PolicyTemplatEextendedParamsDTO policyTemplatEextendedParamsDTO = extendedParams.getOrDefault(paramKey.toString(), new PolicyTemplatEextendedParamsDTO());
                                        policyTemplatEextendedParamsDTO.setExtendedParamsOfLuaTask(sortedParamMap);
                                        List<Integer> policyIdListOfExtendedParams = policyTemplatEextendedParamsDTO.getPolicyIdListOfExtendedParams();
                                        if (policyIdListOfExtendedParams == null) {
                                            policyIdListOfExtendedParams = new ArrayList<>();
                                        }
                                        if (!policyIdListOfExtendedParams.contains(policy.getPolicyId())) {
                                            policyIdListOfExtendedParams.add(policy.getPolicyId());
                                        }
                                        policyTemplatEextendedParamsDTO.setPolicyIdListOfExtendedParams(policyIdListOfExtendedParams);
                                        extendedParams.put(paramKey.toString(), policyTemplatEextendedParamsDTO);
                                    }
                                }
                            }

                            policyTemplateDTO.setExtendedParams(extendedParams);

                            List<Integer> policyIdList = policyTemplateDTO.getPolicyIdList();
                            if (policyIdList == null) {
                                policyIdList = new ArrayList<>();
                            }
                            if (!policyIdList.contains(policy.getPolicyId())) {
                                policyIdList.add(policy.getPolicyId());
                            }
                            policyTemplateDTO.setPolicyIdList(policyIdList);
                            policyTemplateDTOMap.put(luaTask.getLuaTaskNameEn(), policyTemplateDTO);

                        }
                    }
                }

            }
            // 过滤掉应该有参数但是未获取到参数的情况，过滤任务版本与数据版本不一致
            if (MapUtils.isNotEmpty(policyTemplateDTOMap)) {
                Iterator<Map.Entry<String, PolicyTemplateDTO>> iterator = policyTemplateDTOMap.entrySet().iterator();
                while (iterator.hasNext()) {
                    Map.Entry<String, PolicyTemplateDTO> entry = iterator.next();
                    PolicyTemplateDTO policyTemplateDTO = entry.getValue();
                    if ((StringUtils.equalsIgnoreCase("YES", policyTemplateDTO.getLuaTask().getHasParams()) && MapUtils.isEmpty(policyTemplateDTO.getParams())) || (StringUtils.equalsIgnoreCase("YES", policyTemplateDTO.getLuaTask().getHasExtendedParams()) && MapUtils.isEmpty(policyTemplateDTO.getExtendedParams())) || (!appId.equals(policyTemplateDTO.getLuaTask().getAppId()) || !StringUtils.equalsIgnoreCase(sdkVersion, policyTemplateDTO.getLuaTask().getSdkVersion()))) {
                        iterator.remove();
                    }
                }
            }
        }

        // 生成模板文件，并下发策略
        if (MapUtils.isNotEmpty(policyTemplateDTOMap)) {
            // 6.更新策略限制表
            Set<Integer> policyIdSet = new TreeSet<>();
            Set<String> luaTaskSet = new TreeSet<>();

            // 7.更新策略下发记录表
            // 获得记录ID
            PolicyRecordAddParam policyRecordAddParam = new PolicyRecordAddParam();
//            policyRecordAddParam.setDeviceId(deviceId);
            policyRecordAddParam.setReceivedStatus(-1);
            // TODO 保存策略ID避免多线程重复下发问题？
            Integer recordId = policyRecordService.add(policyRecordAddParam);

            // 本次生成策略模板
            StringBuilder luaTaskPolicyTemplateStringNew = new StringBuilder();
            // Lua周期性任务
            StringBuilder luaTimerTaskPolicyTemplateStringNew = new StringBuilder();
            StringBuilder nativeTaskPolicyTemplateStringNew = new StringBuilder();
            for (PolicyTemplateDTO policyTemplateDTO : policyTemplateDTOMap.values()) {
                policyTemplateDTO.setPolicyRecordId(recordId);
                // TODO 匹配版本
                if (StringUtils.equalsIgnoreCase(policyTemplateDTO.getLuaTask().getTaskType(), "lua")) {
                    String nowResult = "";
                    if (StringUtils.isNotBlank(policyTemplateDTO.getLuaTask().getParamsTemplate())) {
                        nowResult = freemarkerService.generateStringByTemplateString(policyTemplateDTO.getLuaTask().getParamsTemplate(), policyTemplateDTO);
                    } else {
                        String templateFile = CloudControllerConstant.LUA_TASK_POLICY_TEMPLATE;
                        if (StrUtil.compareVersion(sdkVersion, "1.12.0.0") >= 0) {
                            templateFile = templateFile + String.format("_v%s", sdkVersion);
                        }
                        nowResult = freemarkerService.generateStringByTemplateFile(templateFile + ".ftl", policyTemplateDTO);
                    }

                    // TODO 周期性任务?
                    if (StrUtil.compareVersion(sdkVersion, "1.12.1.0") >= 0) {
                        Boolean isLuaTimerTask = this.isLuaTimerTask(policyTemplateDTO);
                        if (isLuaTimerTask) {
                            luaTimerTaskPolicyTemplateStringNew.append(nowResult);
                        } else {
                            luaTaskPolicyTemplateStringNew.append(nowResult);
                        }
                    } else {
                        luaTaskPolicyTemplateStringNew.append(nowResult);
                    }

                } else if (StringUtils.equalsIgnoreCase(policyTemplateDTO.getLuaTask().getTaskType(), "native")) {
                    String nowResult = "";
                    if (StringUtils.isNotBlank(policyTemplateDTO.getLuaTask().getParamsTemplate())) {
                        nowResult = freemarkerService.generateStringByTemplateString(policyTemplateDTO.getLuaTask().getParamsTemplate(), policyTemplateDTO);
                    } else {
                        String templateFile = CloudControllerConstant.NATIVE_TASK_POLICY_TEMPLATE;
                        if (StrUtil.compareVersion(sdkVersion, "1.12.0.0") >= 0) {
                            templateFile = templateFile + String.format("_v%s", sdkVersion);
                        }
                        nowResult = freemarkerService.generateStringByTemplateFile(templateFile + ".ftl", policyTemplateDTO);
                    }
                    nativeTaskPolicyTemplateStringNew.append(nowResult);
                } else {
                    log.warn("未知的任务类型，将不执行该任务的下发!TaskType=[{}]", policyTemplateDTO.getLuaTask().getTaskType());
                }

                luaTaskSet.add(policyTemplateDTO.getBaseConfig());
                policyIdSet.addAll(policyTemplateDTO.getPolicyIdList());
            }

            // 合并本次生成和历史模板并下发
            String type = "update";
            Boolean updateResult = this.updatePolicyTemplate(appId, sdkVersion, luaTaskPolicyTemplateStringNew.toString(), luaTimerTaskPolicyTemplateStringNew.toString(), nativeTaskPolicyTemplateStringNew.toString(), type, recordId);

            // 保存数据
            // 6.更新策略限制表
            if (updateResult) {
                // TODO 测试
                policyLimitService.updateDeviceIdByPolicyId(policyIdSet, deviceId);
                // 7.更新策略下发记录表
                PolicyRecordEditParam policyRecordEditParam = new PolicyRecordEditParam();
                policyRecordEditParam.setRecordId(recordId);
                policyRecordEditParam.setDeviceId(deviceId);
                policyRecordEditParam.setAppId(appId);
                policyRecordEditParam.setSdkVersion(sdkVersion);
                policyRecordEditParam.setPolicyIdList(JSONObject.toJSONString(policyIdSet));
                policyRecordEditParam.setLuaTaskList(JSONObject.toJSONString(luaTaskSet));
                policyRecordEditParam.setLuaTaskPolicy(luaTaskPolicyTemplateStringNew.toString());
                policyRecordEditParam.setLuaTimerTaskPolicy(luaTimerTaskPolicyTemplateStringNew.toString());
                policyRecordEditParam.setNativeTaskPolicy(nativeTaskPolicyTemplateStringNew.toString());
                policyRecordEditParam.setReceivedStatus(0);
                // TODO 测试
                policyRecordService.edit(policyRecordEditParam);
            }

        }

        // 1.验证策略限制

        // 2.验证策略下发记录

        // 3.合并参数

        // 3.修改策略模板文件

        // 4。执行脚本生成JSON文件

        // 5.调用远程接口修改策略

        // 6.更新策略限制表

        // 7.更新策略下发记录表

    }

    //    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Integer recordId, String luaName) {
        PolicyRecord policyRecord = policyRecordService.getById(recordId);
        if (policyRecord != null) {
            Integer receivedStatusOld = policyRecord.getReceivedStatus();
            // 非周期性任务记录则更新接收状态(周期性任务记录则不改变状态)
            if (StringUtils.isBlank(policyRecord.getLuaTimerTaskPolicy())) {
                policyRecord.setReceivedStatus(1);
            }
            // 更新接收到的任务
            if (StringUtils.isNotBlank(luaName)) {
                List<String> receivedLuaTaskList = null;
                if (StringUtils.isNotBlank(policyRecord.getReceivedLuaTaskList())) {
                    receivedLuaTaskList = JSONObject.parseArray(policyRecord.getReceivedLuaTaskList(), String.class);
                } else {
                    receivedLuaTaskList = new ArrayList<>();
                }
                if (!receivedLuaTaskList.contains(luaName)) {
                    receivedLuaTaskList.add(luaName);
                }
                if (CollectionUtils.isNotEmpty(receivedLuaTaskList)) {
                    policyRecord.setReceivedLuaTaskList(JSONObject.toJSONString(receivedLuaTaskList));
                } else {
                    policyRecord.setReceivedLuaTaskList(null);
                }
            }
            boolean update = policyRecordService.updateById(policyRecord);
            // 接收状态改变并且更新成功，则重新更新模板
            if ((!policyRecord.getReceivedStatus().equals(receivedStatusOld)) && update) {
                // 重新更新模板
                String type = "delete";
                Integer appId = policyRecord.getAppId();
                String sdkVersion = policyRecord.getSdkVersion();
                this.updatePolicyTemplate(appId, sdkVersion, null, null, null, type, recordId);
            }
        }

    }

    @Override
    public void reloadPolicyTemplate(Integer appId, String sdkVersion) {
        // 重新更新模板
        String type = "reload";
        this.updatePolicyTemplate(appId, sdkVersion, null, null, null, type, 1);
    }

    @Override
    public void downloadLuaTaskFileAndDeletePolicyTemplate(LuaTaskResultDataDTO luaTaskResultDataDTO) {
        String ossBaseUrl = AppConfig.POLICY_DOWNLOAD_OSS_BASE_URL;

        // 下载截图的url
        String luaScreenShot = luaTaskResultDataDTO.getLuaScreenShot();
        // 下载压缩包的url
        String luaZip = luaTaskResultDataDTO.getLuaZip();
        // 下载压缩包的url(大文件)
        List<String> luaLargeZip = luaTaskResultDataDTO.getLuaLargeZip();
        String fileIdOfLuaScreenShot = null;
        String fileIdOfLuaZip = null;
        String base64OfLuaScreenShot = null;
        // 下载截图
        if (StringUtils.isNotBlank(luaScreenShot)) {
            String base = StringUtils.substringBeforeLast(luaScreenShot, "_");
            String suffix = ".bin";
            String filesNumberStr = StringUtils.substringBefore(StringUtils.substringAfterLast(luaScreenShot, "_"), ".");
            if (StringUtils.isNumeric(filesNumberStr)) {
                try {
                    Long filesNumber = Long.valueOf(filesNumberStr);
                    if (filesNumber > 0 && filesNumber < 100L) {
                        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                        for (int i = 0; i < filesNumber; i++) {
                            String url = ossBaseUrl + base + "_" + i + suffix;
                            try {
                                byte[] data = HttpUtil.downloadBytes(url);
                                IOUtils.write(data, outputStream);
                            } catch (Exception e) {
                                log.error("下载截图失败！URL={}", url, e);
                                messageService.sendXingeAdministratorMessage(String.format("下载截图失败！URL=[%s]", url), AppConfig.DPS_ADMINISTRATOR_USERIDS);
                            }
                        }
                        String name = luaScreenShot + ".jpg";
                        byte[] fileData = outputStream.toByteArray();

                        AES aes = new AES(Mode.CBC, Padding.PKCS5Padding, HexToByteUtil.toByte(HexToByteUtil.keyCreateScreenshot), HexToByteUtil.toByte(HexToByteUtil.ivCreateScreenshot));
                        byte[] decryptData = aes.decrypt(fileData);

                        MockMultipartFile mockMultipartFile = new MockMultipartFile(name, name, null, decryptData);
                        fileIdOfLuaScreenShot = this.storageFileWithReturnIdLocal(mockMultipartFile);
                        base64OfLuaScreenShot = Base64.encodeBase64String(decryptData);
                    } else {
                        log.warn("截图URL非法,URL=[{}]", luaScreenShot);
                    }
                } catch (Exception e) {
                    log.warn("解析截图URL异常或者保存文件失败,URL=[{}]", luaScreenShot, e);
                }
            }
        }

        // 下载压缩包
        if (StringUtils.isNotBlank(luaZip)) {
            String base = StringUtils.substringBeforeLast(luaZip, "_");
            String suffix = ".bin";
            String filesNumberStr = StringUtils.substringBefore(StringUtils.substringAfterLast(luaZip, "_"), ".");
            if (StringUtils.isNumeric(filesNumberStr)) {
                try {
                    Long filesNumber = Long.valueOf(filesNumberStr);
                    if (filesNumber > 0 && filesNumber < 100L) {
                        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                        for (int i = 0; i < filesNumber; i++) {
                            String url = ossBaseUrl + base + "_" + i + suffix;
                            try {
                                byte[] data = HttpUtil.downloadBytes(url);
                                IOUtils.write(data, outputStream);
                            } catch (Exception e) {
                                log.error("下载压缩包失败！URL={}", url, e);
                                messageService.sendXingeAdministratorMessage(String.format("下载压缩包失败！URL=[%s]", url), AppConfig.DPS_ADMINISTRATOR_USERIDS);
                            }
                        }
                        String name = luaZip + ".zip";
                        byte[] fileData = outputStream.toByteArray();

                        AES aes = new AES(Mode.CBC, Padding.PKCS5Padding, HexToByteUtil.toByte(HexToByteUtil.keyUploadFiles), HexToByteUtil.toByte(HexToByteUtil.ivUploadFiles));
                        byte[] decryptData = aes.decrypt(fileData);

                        MockMultipartFile mockMultipartFile = new MockMultipartFile(name, name, null, decryptData);
                        fileIdOfLuaZip = this.storageFileWithReturnIdLocal(mockMultipartFile);
                    } else {
                        log.warn("压缩包URL非法,URL=[{}]", luaZip);
                    }
                } catch (Exception e) {
                    log.warn("解析压缩包URL异常或者保存文件失败,URL=[{}]", luaZip, e);
                }
            }
        }

        // 下载压缩包(大文件)
        if (CollectionUtils.isNotEmpty(luaLargeZip)) {
            List<String> luaPolicyIdMatchParam = luaTaskResultDataDTO.getLuaPolicyIdMatchParam();
            // 生成ZIP压缩文件
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ZipOutputStream zipOutputStream = new ZipOutputStream(outputStream);
            for (int zipNum = 0; zipNum < CollectionUtils.size(luaLargeZip); zipNum++) {
                String luaZipTmp = luaLargeZip.get(zipNum);
                String fileName = luaPolicyIdMatchParam.get(zipNum);
                if (StringUtils.isNotBlank(luaZipTmp)) {
                    String base = StringUtils.substringBeforeLast(luaZipTmp, "_");
                    String suffix = ".bin";
                    String filesNumberStr = StringUtils.substringBefore(StringUtils.substringAfterLast(luaZipTmp, "_"), ".");
                    if (StringUtils.isNumeric(filesNumberStr)) {
                        try {
                            Long filesNumber = Long.valueOf(filesNumberStr);
                            if (filesNumber > 0 && filesNumber < 1000L) {
                                // 单个文件
                                ByteArrayOutputStream outputStreamTmp = new ByteArrayOutputStream();
                                for (int i = 0; i < filesNumber; i++) {
                                    String url = ossBaseUrl + base + "_" + i + suffix;
                                    LocalDateTime startTime = LocalDateTime.now();
                                    log.info("开始执行文件下载......" + url);
                                    try {
                                        // 下载
                                        byte[] data = HttpUtil.downloadBytes(url);
                                        // 解密
                                        AES aes = new AES(Mode.CBC, Padding.PKCS5Padding, HexToByteUtil.toByte(HexToByteUtil.keyUploadLargeFiles), HexToByteUtil.toByte(HexToByteUtil.ivUploadLargeFiles));
                                        byte[] decryptData = aes.decrypt(data);
                                        // 解压
                                        ByteArrayOutputStream unZlibOutputStream = new ByteArrayOutputStream(decryptData.length);
                                        Deflate.of(new ByteArrayInputStream(decryptData), unZlibOutputStream, true).inflater();
                                        byte[] unZlibData = unZlibOutputStream.toByteArray();
                                        // 拼接单个文件
                                        IOUtils.write(unZlibData, outputStreamTmp);
                                    } catch (Exception e) {
                                        log.error("下载压缩包失败！URL={}", url, e);
                                        messageService.sendXingeAdministratorMessage(String.format("下载压缩包失败！URL=[%s]", url), AppConfig.DPS_ADMINISTRATOR_USERIDS);
                                    }
                                    LocalDateTime endTime = LocalDateTime.now();
                                    Duration duration = Duration.between(startTime, endTime);
                                    log.info(String.format("文件下载成功,耗时=[%s]ms。", duration.toMillis()));
                                }
                                // 将单个文件放入压缩包
                                try {
                                    zipOutputStream.putNextEntry(new ZipEntry(fileName));
                                    IOUtils.write(outputStreamTmp.toByteArray(), zipOutputStream);
                                } catch (Exception e) {
                                    log.error("下载文件失败!", e);
                                } finally {
                                    IOUtils.closeQuietly(outputStreamTmp);
                                }
                            } else {
                                log.warn("压缩包URL非法,URL=[{}]", luaZipTmp);
                            }
                        } catch (Exception e) {
                            log.warn("解析压缩包URL异常或者保存文件失败,URL=[{}]", luaZipTmp, e);
                        }
                    }
                }
            }
            try {
                zipOutputStream.closeEntry();
                IOUtils.closeQuietly(zipOutputStream);
            } catch (IOException e) {
                log.error("下载文件失败!", e);
            }
            String name = luaTaskResultDataDTO.getLuaName() + "_" + luaTaskResultDataDTO.getDeviceId() + "_" + DateFormatUtils.format(luaTaskResultDataDTO.getPreProcessingTime() * 1000L, "yyyyMMddHHmmss") + ".zip";
            byte[] fileData = outputStream.toByteArray();
            MockMultipartFile mockMultipartFile = new MockMultipartFile(name, name, null, fileData);
            fileIdOfLuaZip = this.storageFileWithReturnIdLocal(mockMultipartFile);
        }

        if (StringUtils.isNotBlank(fileIdOfLuaScreenShot) && StringUtils.isNotBlank(fileIdOfLuaZip)) {
            log.warn("注意：同时存在截图和压缩包文件，可能导致数据丢失!fileIdOfLuaScreenShot=[{}],fileIdOfLuaZip=[{}],luaTaskResultDataDTO=[{}]", fileIdOfLuaScreenShot, fileIdOfLuaZip, JSONObject.toJSONString(luaTaskResultDataDTO));
        }

        String fileId = StringUtils.firstNonBlank(fileIdOfLuaScreenShot, fileIdOfLuaZip);
        // TODO 保存结果记录
        CloudControllerResultAddParam cloudControllerResultAddParam = new CloudControllerResultAddParam();
        cloudControllerResultAddParam.setFileId(fileId);
        cloudControllerResultAddParam.setLuaName(luaTaskResultDataDTO.getLuaName());
        // 可能出现设备ID和主机名无法获取到的情况
        cloudControllerResultAddParam.setDeviceId(StringUtils.firstNonBlank(luaTaskResultDataDTO.getDeviceId(), luaTaskResultDataDTO.getHardwareHostName(), "unknown"));
        cloudControllerResultAddParam.setHostName(StringUtils.firstNonBlank(luaTaskResultDataDTO.getHardwareHostName(), cloudControllerResultAddParam.getDeviceId()));
        if (CollectionUtils.isNotEmpty(luaTaskResultDataDTO.getLuaPolicyRecordId())) {
            cloudControllerResultAddParam.setRecordIds(JSONObject.toJSONString(luaTaskResultDataDTO.getLuaPolicyRecordId()));
        }
        if (CollectionUtils.isNotEmpty(luaTaskResultDataDTO.getLuaPolicyIdList())) {
            cloudControllerResultAddParam.setPolicyIds(JSONObject.toJSONString(luaTaskResultDataDTO.getLuaPolicyIdList()));
        }
        cloudControllerResultAddParam.setDataJson(JSONObject.toJSONString(luaTaskResultDataDTO));
        cloudControllerResultAddParam.setCreateTime(DateUtil.date(luaTaskResultDataDTO.getPreProcessingTime() * 1000L));
        cloudControllerResultAddParam.setUpdateTime(new Date());
        // 结果ID
        String resultId = cloudControllerResultService.add(cloudControllerResultAddParam);

        // 保存策略与结果关联
        if (CollectionUtils.isNotEmpty(luaTaskResultDataDTO.getLuaPolicyIdList())) {
            List<PolicyResultLink> policyResultLinkList = new ArrayList<>();
            for (Long policyId : luaTaskResultDataDTO.getLuaPolicyIdList()) {
                if (policyId != null) {
                    PolicyResultLink policyResultLink = new PolicyResultLink();
                    policyResultLink.setPolicyId(policyId);
                    policyResultLink.setResultId(resultId);
                    CollectionUtils.addIgnoreNull(policyResultLinkList, policyResultLink);
                }
            }
            policyResultLinkService.saveBatch(policyResultLinkList);
        }
        // 保存下发记录与结果关联
        if (CollectionUtils.isNotEmpty(luaTaskResultDataDTO.getLuaPolicyRecordId())) {
            List<RecordResultLink> recordResultLinkList = new ArrayList<>();
            for (Long recordId : luaTaskResultDataDTO.getLuaPolicyRecordId()) {
                if (recordId != null) {
                    RecordResultLink recordResultLink = new RecordResultLink();
                    recordResultLink.setRecordId(recordId);
                    recordResultLink.setResultId(resultId);
                    CollectionUtils.addIgnoreNull(recordResultLinkList, recordResultLink);
                }
            }
            recordResultLinkService.saveBatch(recordResultLinkList);
        }

        // 保存策略限制表中已回传的设备ID列表
        if (CollectionUtils.isNotEmpty(luaTaskResultDataDTO.getLuaPolicyIdList())) {
            policyLimitService.updateReceivedDeviceIdByPolicyId(new TreeSet<>(luaTaskResultDataDTO.getLuaPolicyIdList()), cloudControllerResultAddParam.getDeviceId());
        }

        // TODO 更新策略记录表，保存任务回传情况

        // 重新更新策略模板
        List<Long> luaPolicyRecordId = luaTaskResultDataDTO.getLuaPolicyRecordId();
        if (CollectionUtils.isNotEmpty(luaPolicyRecordId)) {
            for (Long recordId : luaPolicyRecordId) {
                // recordId=0 表示非动态策略下发任务执行，无需删除策略
                if (!Long.valueOf(0).equals(recordId)) {
                    this.delete(recordId.intValue(), luaTaskResultDataDTO.getLuaName());
                }
            }
        }

        // 异步更新识别结果
        if (StringUtils.isNotBlank(fileIdOfLuaScreenShot) && StringUtils.isNotBlank(base64OfLuaScreenShot)) {
            this.setAiDetectResult(resultId, fileIdOfLuaScreenShot, base64OfLuaScreenShot);
        }

        // TODO 办公端回传结果处理
        // 是否为办公端,0或空-否,1-是
        if (luaTaskResultDataDTO.getOffice() != null && Integer.valueOf(1).equals(luaTaskResultDataDTO.getOffice())) {
            networkManageService.update(luaTaskResultDataDTO);
        }

    }

    private Boolean updatePolicyTemplate(Integer appId, String sdkVersion, String luaTaskPolicyTemplateStringNew, String luaTimerTaskPolicyTemplateStringNew, String nativeTaskPolicyTemplateStringNew, String type, Integer id) {
        Boolean updateResult = Boolean.FALSE;
        if (appId == null || StringUtils.isBlank(sdkVersion)) {
            log.warn("更新策略模板接收到的应用ID或SDK版本为空，无法确定策略上传地址，将不执行策略模板更新!type=[{}],id=[{}],appId=[{}],sdkVersion=[{}],luaTaskPolicyTemplateStringNew=[{}],nativeTaskPolicyTemplateStringNew=[{}]", type, id, appId, sdkVersion, luaTaskPolicyTemplateStringNew, nativeTaskPolicyTemplateStringNew);
            return updateResult;
        }
        // 模板文件名和云控URL通过SDK版本进行转换
        String templateFileNameAndUrlString = this.getTemplateFileNameAndUrl(String.format("%s_v%s", appId, sdkVersion));
        String templateFileName = null;
        String url = null;
        if (StringUtils.isNotBlank(templateFileNameAndUrlString)) {
            templateFileName = StringUtils.substringBefore(templateFileNameAndUrlString, "=");
            url = StringUtils.substringAfter(templateFileNameAndUrlString, "=");
        }
        if (StringUtils.isBlank(templateFileNameAndUrlString) || StringUtils.isBlank(templateFileName) || StringUtils.isBlank(url)) {
            log.warn("更新策略模板接收到的应用ID和SDK版本通过转换模板文件和云控地址为空，无法确定策略上传地址，将不执行策略模板更新!type=[{}],id=[{}],appId=[{}],sdkVersion=[{}],luaTaskPolicyTemplateStringNew=[{}],nativeTaskPolicyTemplateStringNew=[{}]", type, id, appId, sdkVersion, luaTaskPolicyTemplateStringNew, nativeTaskPolicyTemplateStringNew);
            return updateResult;
        }

        //    log.info("更新策略模板!type=[{}],id=[{}],appId=[{}],sdkVersion=[{}],templateFileName=[{}],url=[{}],luaTaskPolicyTemplateStringNew=[{}],nativeTaskPolicyTemplateStringNew=[{}]", type, id, appId, sdkVersion, templateFileName, url, luaTaskPolicyTemplateStringNew, nativeTaskPolicyTemplateStringNew);

        // 策略更新开关关闭则不执行更新(在 系统-->系统运维-->系统配置-->其他配置-->POLICY_UPDATE_ENABLE 中配置)
        String updateEnable = this.getDevConfigValueByKey("POLICY_UPDATE_ENABLE");
        if (!StringUtils.equalsIgnoreCase(updateEnable, Boolean.TRUE.toString())) {
            log.warn("更新策略模板功能关闭，将不执行策略模板更新!type=[{}],id=[{}],luaTaskPolicyTemplateStringNew=[{}],nativeTaskPolicyTemplateStringNew=[{}]", type, id, luaTaskPolicyTemplateStringNew, nativeTaskPolicyTemplateStringNew);
            return updateResult;
        }
        // 历史策略模板
        Map<String, Set<String>> policyTemplateMap = policyRecordService.listPolicyTemplateByReceivedStatus(appId, sdkVersion, 0);
        Set<String> luaTaskPolicyTemplates = policyTemplateMap.getOrDefault("luaTaskPolicyTemplates", new LinkedHashSet<>());
        Set<String> luaTimerTaskPolicyTemplates = policyTemplateMap.getOrDefault("luaTimerTaskPolicyTemplates", new LinkedHashSet<>());
        Set<String> nativeTaskPolicyTemplates = policyTemplateMap.getOrDefault("nativeTaskPolicyTemplates", new LinkedHashSet<>());

        // 本次生成模板
        if (StringUtils.isNotBlank(luaTaskPolicyTemplateStringNew)) {
            luaTaskPolicyTemplates.add(luaTaskPolicyTemplateStringNew);
        }
        if (StringUtils.isNotBlank(luaTimerTaskPolicyTemplateStringNew)) {
            luaTimerTaskPolicyTemplates.add(luaTimerTaskPolicyTemplateStringNew);
        }
        if (StringUtils.isNotBlank(nativeTaskPolicyTemplateStringNew)) {
            nativeTaskPolicyTemplates.add(nativeTaskPolicyTemplateStringNew);
        }

        String luaTaskPolicyTemplateString = StringUtils.join(luaTaskPolicyTemplates, "");
        String luaTimerTaskPolicyTemplateString = StringUtils.join(luaTimerTaskPolicyTemplates, "");
        String nativeTaskPolicyTemplateString = StringUtils.join(nativeTaskPolicyTemplates, "");
        // 本次生成和历史模板
        Map<String, Object> map = new HashMap<>();
        map.put("luaTaskPolicyTemplateString", luaTaskPolicyTemplateString);
        map.put("luaTimerTaskPolicyTemplateString", luaTimerTaskPolicyTemplateString);
        map.put("nativeTaskPolicyTemplateString", nativeTaskPolicyTemplateString);
        // luaCodeMap
        Map<String, String> luaCodeMap = luaTaskService.listLuaCode(appId, sdkVersion);
        // isForceExecutionMap
        Map<String, String> isForceExecutionMap = luaTaskService.listIsForceExecutionMap(appId, sdkVersion);
        map.put("luaCodeMap", luaCodeMap);
        map.put("isForceExecutionMap", isForceExecutionMap);
        // TODO 模板通过SDK版本进行转换
//        String dps83String = freemarkerService.generateStringByTemplateFile(CloudControllerConstant.DPS_83_TEMPLATE, map);
        String dps83String = freemarkerService.generateStringByTemplateFile(templateFileName, map);
        String pythonCmd = AppConfig.POLICY_TEMPLATE_PYTHON_CMD;
        String filePathPy = AppConfig.POLICY_TEMPLATE_FILE_PATH_PY;
        String filePathName = AppConfig.POLICY_TEMPLATE_FILE_PATH_OUT + File.separator + DateUtil.date().toDateStr() + File.separator + type + File.separator + id + "_" + DateUtil.date().toString(DatePattern.PURE_DATETIME_MS_PATTERN);
        String filePath83 = filePathName + ".83";
        String filePathJson = filePathName + ".json";
        File file83 = new File(filePath83);
        File fileJson = new File(filePathJson);
        try {
            if (!file83.getParentFile().exists()) {
                FileUtils.createParentDirectories(file83);
            }
            if (!fileJson.getParentFile().exists()) {
                FileUtils.createParentDirectories(fileJson);
            }

            FileUtils.writeStringToFile(file83, dps83String, "UTF-8");
            // 执行脚本
            String exec = pythonCmd + " " + filePathPy + " " + filePath83 + " " + filePathJson;
            String str = RuntimeUtil.execForStr(exec);
            log.info("脚本执行结果=[{}]", str);
            // 文件存在则表示生成JSON的脚本执行成功
            if (file83.exists() && fileJson.exists()) {
                String json = FileUtils.readFileToString(fileJson, "UTF-8");
//                log.info("生成json=[{}]", json);

                if (StringUtils.isNotBlank(json)) {
                    // 5.调用远程接口修改策略
                    // 6.更新策略限制表
                    // 7.更新策略下发记录表
                    String token = AppConfig.POLICY_TEMPLATE_MANAGE_TOKEN;
//                log.info("更新策略远程调用=[{}]", json);
                    String bodyString = HttpRequest.put(url).header("X-Consul-Token", token).body(json).timeout(5 * 1000).execute().body();
                    if (StringUtils.equalsIgnoreCase("true", bodyString)) {
                        updateResult = Boolean.TRUE;
                        log.info("更新策略模板文件成功，远程调用返回结果=[{}],type=[{}],id=[{}]", bodyString, type, id);
                    } else {
                        log.error("更新策略模板文件失败，远程调用返回结果=[{}],type=[{}],id=[{}],luaTaskPolicyTemplateStringNew=[{}],nativeTaskPolicyTemplateStringNew=[{}]", bodyString, type, id, luaTaskPolicyTemplateStringNew, nativeTaskPolicyTemplateStringNew);
                    }
                }
            } else {
                log.error("执行策略更新失败");
                throw new CommonException("执行策略更新失败,type=[{}],id=[{}],luaTaskPolicyTemplateStringNew=[{}],nativeTaskPolicyTemplateStringNew=[{}]", type, id, luaTaskPolicyTemplateStringNew, nativeTaskPolicyTemplateStringNew);
            }
        } catch (Exception e) {
            log.error("执行策略更新报错：", e);
            throw new CommonException("执行策略更新报错,type=[{}],id=[{}],luaTaskPolicyTemplateStringNew=[{}],nativeTaskPolicyTemplateStringNew=[{}]", type, id, luaTaskPolicyTemplateStringNew, nativeTaskPolicyTemplateStringNew);
        } finally {
            // TODO 删除临时文件
//            FileUtils.deleteQuietly(file83);
//            FileUtils.deleteQuietly(fileJson);
        }
        return updateResult;
    }

    /**
     * 上传文件返回Id（需要切换数据库）
     *
     * @param file 文件
     * <AUTHOR>
     * @date 2022/6/22 17:44
     **/
    private String storageFileWithReturnIdLocal(MultipartFile file) {
        // 手动切换数据源
        DynamicDataSourceContextHolder.push(DataSourceConstant.DYNAMIC_DATASOURCE_MASTER);
        String fileId = devFileApi.storageFileWithReturnIdLocal(file);
        // 出栈手动切换的数据源(即回到当前数据源)，否则下一个请求遇到这个线程就会带进来
        DynamicDataSourceContextHolder.poll();
        return fileId;
    }

    /**
     * 根据键获取配置值
     *
     * @param key 键
     * @return 配置值
     */
    private String getDevConfigValueByKey(String key) {
//        System.out.println("切换之前的数据源:" + DynamicDataSourceContextHolder.peek());
        // 手动切换数据源
        DynamicDataSourceContextHolder.push(DataSourceConstant.DYNAMIC_DATASOURCE_MASTER);
        String value = devConfigApi.getValueByKey(key);
//        System.out.println("切换之后的数据源:" + DynamicDataSourceContextHolder.peek());
        // 出栈手动切换的数据源(即回到当前数据源)，否则下一个请求遇到这个线程就会带进来
        DynamicDataSourceContextHolder.poll();
//        System.out.println("出栈之后的数据源:" + DynamicDataSourceContextHolder.peek());
        return value;
    }

    /**
     * 根据模板文件路径和云控上传地址
     *
     * @param key 键
     * @return 配置值
     */
    private String getTemplateFileNameAndUrl(String key) {
        String templateFileNameAndUrlMapStr = this.getDevConfigValueByKey("CLOUD_CONTROLLER_POLICY_TEMPLATE_MANAGE_TEMPLATE_FILE_NAME_AND_URL");
        if (StringUtils.isNotBlank(templateFileNameAndUrlMapStr)) {
            JSONObject templateFileNameAndUrlMap = JSONObject.parseObject(templateFileNameAndUrlMapStr);
            return templateFileNameAndUrlMap.getString(key);
        } else {
            log.warn("解析模板文件名和云控URL(在 系统-->系统运维-->系统配置-->其他配置-->CLOUD_CONTROLLER_POLICY_TEMPLATE_MANAGE_TEMPLATE_FILE_NAME_AND_URL 中配置)异常，templateFileNameAndUrlMapStr=[{}]", templateFileNameAndUrlMapStr);
        }
        return null;
    }

    private void setAiDetectResult(String resultId, String fileIdOfLuaScreenShot, String base64OfLuaScreenShot) {
//        log.info("--------------------- 准备进入AI接口调用线程,resultId=[{}] ---------------------", resultId);
        ThreadUtil.execAsync(() -> {
            try {
//                log.info("--------------------- AI接口调用 开始 ---------------------");
                LocalDateTime startTime = LocalDateTime.now();
                JSONObject params = new JSONObject();
                params.put("image_index", fileIdOfLuaScreenShot);
                params.put("base64_image", base64OfLuaScreenShot);
                JSONObject remoteResult = aiRemoteService.detect(params);
                if (remoteResult != null && StringUtils.isNotBlank(remoteResult.getString("result"))) {
                    CloudControllerResult cloudControllerResult = cloudControllerResultService.queryEntity(resultId);
                    cloudControllerResult.setAbResult(JSONObject.toJSONString(remoteResult.getJSONArray("result")));
                    cloudControllerResult.setAbContent(remoteResult.getString("content"));
                    cloudControllerResultService.updateById(cloudControllerResult);
                } else {
                    log.error("远程调用AI识图接口报错,resultId=[{}],远程返回结果=[{}],base64OfLuaScreenShot=[{}]", resultId, JSONObject.toJSONString(remoteResult), base64OfLuaScreenShot);
                }
                LocalDateTime endTime = LocalDateTime.now();
                Duration duration = Duration.between(startTime, endTime);
                log.info(String.format("--------------------- AI接口调用 结束(耗时=[%s]ms) ---------------------", duration.toMillis()));
            } catch (Exception e) {
                log.error("异步更新识别结果失败,resultId=[{}]", resultId, e);
            }
        }, false);
    }

    /**
     * 是否为Lua周期性任务
     *
     * @param policyTemplateDTO policyTemplateDTO
     * @return 是否为Lua周期性任务
     */
    private Boolean isLuaTimerTask(PolicyTemplateDTO policyTemplateDTO) {
        Boolean isLuaTimerTask = Boolean.FALSE;
        if (StringUtils.equalsIgnoreCase(policyTemplateDTO.getLuaTask().getLuaTaskNameEn(), "create_screen_shot")) {
            Map<String, List<Integer>> paramsOfLuaTimerTask = policyTemplateDTO.getParams();
            if (MapUtils.isNotEmpty(paramsOfLuaTimerTask)) {
                isLuaTimerTask = Boolean.TRUE;
            }
        }
        return isLuaTimerTask;
    }

}
