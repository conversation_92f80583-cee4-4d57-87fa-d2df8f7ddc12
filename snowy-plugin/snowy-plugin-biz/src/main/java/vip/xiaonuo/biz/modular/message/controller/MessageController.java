package vip.xiaonuo.biz.modular.message.controller;

import cn.dev33.satoken.annotation.SaCheckBasic;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vip.xiaonuo.biz.core.config.AppConfig;
import vip.xiaonuo.biz.modular.message.dto.SendRobotDTO;
import vip.xiaonuo.biz.modular.message.service.MessageService;
import vip.xiaonuo.biz.modular.taguseridlog.param.TagUseridLogAddParam;
import vip.xiaonuo.biz.modular.taguseridlog.service.TagUseridLogService;
import vip.xiaonuo.common.pojo.CommonResult;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/11/3 14:52
 */
@Api(tags = "消息发送控制器")
@ApiSupport(author = "SNOWY_TEAM", order = 1)
@RestController
@Validated
@Slf4j
@RequestMapping("biz/message")
public class MessageController {

    @Resource
    private MessageService messageService;

    @Resource
    private TagUseridLogService tagUseridLogService;

    /**
     * 机器人发送群消息
     *
     * <AUTHOR>
     * @date 2023/03/17 17:26
     */
    @ApiOperationSupport(order = 2)
    @ApiOperation("机器人发送群消息")
    @PostMapping("sendDDRobot")
    public CommonResult<String> sendDDRobot(@RequestBody @Valid SendRobotDTO sendRobotDTO) {
        Map<String, String> text = new HashMap<>();
        text.put("content", sendRobotDTO.getText());
        sendRobotDTO.setToken("499bbcb43bb9704c3e6a8f46ecab9de92133a5d50a938e798455439df94366e5");
        sendRobotDTO.setMsgtype("text");
        sendRobotDTO.setText(JSONObject.toJSONString(text));
        log.info("发送的群消息=[{}]", JSONObject.toJSONString(sendRobotDTO));
        messageService.sendDDRobot(sendRobotDTO);
        return CommonResult.ok();
    }

    /**
     * 机器人发送群消息
     *
     * <AUTHOR>
     * @date 2023/03/17 17:26
     */
    @ApiOperationSupport(order = 2)
    @ApiOperation("机器人发送群消息(红玄传)")
    @PostMapping("sendDDRobotHxz")
    @SaCheckBasic
    public CommonResult<String> sendDDRobotHxz(@RequestBody JSONObject jsonObject) {

        String tagNameEn = "is_login_mps_wzwd_plugin01_15d";
        Long userId = jsonObject.getLong("uid");
        Long eventTime = jsonObject.getLong("event_time");
        if (userId != null) {
            boolean hasUserId = tagUseridLogService.hasUserId(userId, tagNameEn);

            // 保存记录
            TagUseridLogAddParam tagUseridLogAddParam = new TagUseridLogAddParam();
            tagUseridLogAddParam.setDay(DateUtil.date(eventTime * 1000L).toDateStr());
            tagUseridLogAddParam.setUserId(userId);
            tagUseridLogAddParam.setTagNameEn(tagNameEn);
            tagUseridLogAddParam.setCreateTime(DateUtil.date(eventTime * 1000L));
            tagUseridLogAddParam.setUpdateTime(DateUtil.date());
            if (!hasUserId) {
                String bodyNotBase64 = String.format("JJ Neural 安全风控告警：\n" +
                        "新增用户[ %s ]安装[红玄传外挂类型一]。\n请留意!\n" +
                        "此消息发送时间：%s\n", userId, DateUtil.formatDateTime(new Date()));

                Map<String, String> text = new HashMap<>();
                text.put("content", bodyNotBase64);
                SendRobotDTO sendRobotDTO = new SendRobotDTO();
//                sendRobotDTO.setToken(AppConfig.ROBOT_MESSAGE_TOKEN_OF_TAG.getOrDefault("123", AppConfig.ROBOT_MESSAGE_TOKEN_OF_DEFAULT));
                sendRobotDTO.setToken(AppConfig.ROBOT_MESSAGE_TOKEN_OF_TAG.getOrDefault("123", "d3fcbf08aa8c39ee14386a1d8f9f17e0e29d571a56de363ff36c1627fd632853"));
                sendRobotDTO.setMsgtype("text");
                sendRobotDTO.setText(JSONObject.toJSONString(text));
                log.info("发送的群消息=[{}]", JSONObject.toJSONString(sendRobotDTO));
                messageService.sendDDRobot(sendRobotDTO);
                // 状态，-1数据无效，0不发送告警，1发送告警
                tagUseridLogAddParam.setStatus("1");
                // 保存触发记录
                tagUseridLogService.add(tagUseridLogAddParam);
            } else {
                // 状态，-1数据无效，0不发送告警，1发送告警
                tagUseridLogAddParam.setStatus("0");
                // 保存触发记录
                tagUseridLogService.add(tagUseridLogAddParam);
                return CommonResult.ok(String.format("该用户ID=[%s]已存在，本次不告警", userId));
            }
        }
        return CommonResult.ok(String.format("该用户ID=[%s]发送告警成功", userId));
    }

}
