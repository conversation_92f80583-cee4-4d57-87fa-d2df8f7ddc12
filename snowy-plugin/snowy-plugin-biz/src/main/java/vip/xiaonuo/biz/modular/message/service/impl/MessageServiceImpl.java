package vip.xiaonuo.biz.modular.message.service.impl;

import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import vip.xiaonuo.biz.core.config.AppConfig;
import vip.xiaonuo.biz.modular.alarm.constant.MessageConstant;
import vip.xiaonuo.biz.modular.alarm.dto.MergedTemplateDataDTO;
import vip.xiaonuo.biz.modular.alarm.dto.TemplateData;
import vip.xiaonuo.biz.modular.freemarker.service.FreemarkerService;
import vip.xiaonuo.biz.modular.message.dto.SendEmailDTO;
import vip.xiaonuo.biz.modular.message.dto.SendRobotDTO;
import vip.xiaonuo.biz.modular.message.dto.SendXingeDTO;
import vip.xiaonuo.biz.modular.message.service.MessageService;
import vip.xiaonuo.biz.modular.remote.dto.TokenEsbDTO;
import vip.xiaonuo.biz.modular.remote.dto.TokenEsbRequestParamDTO;
import vip.xiaonuo.biz.modular.remote.service.EsbRemoteService;
import com.alibaba.fastjson.JSONObject;
import feign.Response;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import vip.xiaonuo.biz.modular.remote.service.OpenApiRemoteService;

import java.util.*;

/**
 * Description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/3/1 16:27
 */
@Service
@Slf4j
public class MessageServiceImpl implements MessageService {

    @Autowired
    private EsbRemoteService esbRemoteService;

    @Autowired
    private FreemarkerService freemarkerService;

    @Autowired
    private OpenApiRemoteService openApiRemoteService;

//    @Autowired
//    private CacheManager cacheManager;

    @Override
    public TokenEsbDTO defaultTokenEsb(Boolean refreshToken) {
        // TODO 补充缓存功能
        TokenEsbDTO tokenEsbDTO = null;
//        refreshToken = BooleanUtils.toBooleanDefaultIfNull(refreshToken, Boolean.FALSE);

        // 如果不刷新则直接获取缓存
//        if (!refreshToken) {
//            Cache cache = cacheManager.getCache(CachingConstant.DEFAULT_TOKEN_ESB_CACHE_CACHE_NAME);
//            tokenEsbDTO = cache.get(CachingConstant.DEFAULT_TOKEN_ESB_CACHE_CACHE_KEY, TokenEsbDTO.class);
//            log.debug("从缓存中获取token结果tokenEsbDTO=[{}]", JSONObject.toJSONString(tokenEsbDTO));
//            if (tokenEsbDTO != null) {
//                return tokenEsbDTO;
//            }
//        }

        // 刷新token
        TokenEsbRequestParamDTO tokenEsbRequestParamDTO = new TokenEsbRequestParamDTO();
        tokenEsbRequestParamDTO.setAccount(AppConfig.ESB_ACCOUNT);
        tokenEsbRequestParamDTO.setPassword(AppConfig.ESB_PASSWORD);
        try {
            tokenEsbDTO = esbRemoteService.tokenEsb(tokenEsbRequestParamDTO);
            log.debug("刷新token成功，tokenEsbDTO=[{}]", JSONObject.toJSONString(tokenEsbDTO));
        } catch (Exception e) {
            log.error("使用默认账号密码获取esbToken失败!", e);
        }

        // 更新缓存
//        Cache cache = cacheManager.getCache(CachingConstant.DEFAULT_TOKEN_ESB_CACHE_CACHE_NAME);
//        cache.put(CachingConstant.DEFAULT_TOKEN_ESB_CACHE_CACHE_KEY, tokenEsbDTO);

        return tokenEsbDTO;
    }

    @Override
    public boolean sendXingeV2(SendXingeDTO sendXingeDTO) {
        if (!AppConfig.ESB_XINGE_ENABLED || sendXingeDTO == null || StringUtils.isBlank(sendXingeDTO.getBodyNotBase64()) || CollectionUtils.isEmpty(sendXingeDTO.getReceivers())) {
            log.warn("信鸽通知处于关闭状态或消息为空，将无法发送信鸽通知!sendXingeDTO={}", JSONObject.toJSONString(sendXingeDTO));
            return Boolean.FALSE;
        }
        // 使用默认账号密码获取esbToken
        TokenEsbDTO tokenEsbDTO = this.defaultTokenEsb(null);
        sendXingeDTO.setSource(String.valueOf(tokenEsbDTO.getAppId()));
        Response response = esbRemoteService.sendXingeV2(sendXingeDTO, tokenEsbDTO.getToken());

        // 远程返回401，token失效，强制刷新token并重试
        if (response.status() == HttpStatus.SC_UNAUTHORIZED) {
            tokenEsbDTO = this.defaultTokenEsb(Boolean.TRUE);
            sendXingeDTO.setSource(String.valueOf(tokenEsbDTO.getAppId()));
            log.warn(String.format("第一次发送信鸽消息失败，原因token失效,将强制刷新token并重试，第一次发送信鸽消息返回结果status=[%s],headers=[%s],body=[%s],String=[%s],强制刷新token后tokenEsbDTO=[%s]", response.status(), JSONObject.toJSONString(response.headers()), JSONObject.toJSONString(response.body()), JSONObject.toJSONString(response.toString()), JSONObject.toJSONString(tokenEsbDTO)));
            response = esbRemoteService.sendXingeV2(sendXingeDTO, tokenEsbDTO.getToken());
        }

        // 发送结果，远程返回200表示发送成功
        Boolean result = response.status() == HttpStatus.SC_OK ? Boolean.TRUE : Boolean.FALSE;
        if (result) {
            log.info(String.format("发送信鸽消息成功!接收人列表receivers=%s,返回结果status=[%s]", JSONObject.toJSONString(sendXingeDTO.getReceivers()), response.status()));
        } else {
            log.warn(String.format("发送信鸽消息失败!接收人列表receivers=%s,sendXingeDTO=[%s],返回结果status=[%s],headers=[%s],body=[%s],String=[%s]", JSONObject.toJSONString(sendXingeDTO.getReceivers()), JSONObject.toJSONString(sendXingeDTO), response.status(), JSONObject.toJSONString(response.headers()), JSONObject.toJSONString(response.body()), JSONObject.toJSONString(response.toString())));
        }

        return result;
    }

    @Override
    public boolean sendEmail(SendEmailDTO sendEmailDTO) {
        if (!AppConfig.ESB_EMAIL_ENABLED || sendEmailDTO == null || StringUtils.isBlank(sendEmailDTO.getBodyNotBase64()) || CollectionUtils.isEmpty(sendEmailDTO.getEmailTo())) {
            log.warn("邮件通知处于关闭状态或消息为空，将无法发送邮件通知!sendEmailDTO={}", JSONObject.toJSONString(sendEmailDTO));
            return Boolean.FALSE;
        }
        // 使用默认账号密码获取esbToken
        TokenEsbDTO tokenEsbDTO = this.defaultTokenEsb(null);
        sendEmailDTO.setSource(String.valueOf(tokenEsbDTO.getAppId()));
        // 发送邮件需要对整个 msgjson 的内容做一下 urlencode，因此需要使用MultiValueMap，并且设置为application/x-www-form-urlencoded
        String msgJson = JSONObject.toJSONString(sendEmailDTO);
        MultiValueMap<String, String> multiValueMap = new LinkedMultiValueMap<>();
        multiValueMap.set("msgJson", msgJson);
        Response response = esbRemoteService.sendEmail(multiValueMap, tokenEsbDTO.getToken());

        // 远程返回401，token失效，强制刷新token并重试
        if (response.status() == HttpStatus.SC_UNAUTHORIZED) {
            tokenEsbDTO = this.defaultTokenEsb(Boolean.TRUE);
            log.warn(String.format("第一次发送邮件失败，原因token失效,将强制刷新token并重试，第一次发送邮件返回结果status=[%s],headers=[%s],body=[%s],String=[%s],强制刷新token后tokenEsbDTO=[%s]", response.status(), JSONObject.toJSONString(response.headers()), JSONObject.toJSONString(response.body()), JSONObject.toJSONString(response.toString()), JSONObject.toJSONString(tokenEsbDTO)));
            response = esbRemoteService.sendEmail(multiValueMap, tokenEsbDTO.getToken());
        }

        // 发送结果，远程返回200表示发送成功
        Boolean result = response.status() == HttpStatus.SC_OK ? Boolean.TRUE : Boolean.FALSE;
        if (result) {
            log.info(String.format("发送邮件成功!接收人列表emailTo=%s,抄送人列表emailCC=%s,返回结果status=[%s]", JSONObject.toJSONString(sendEmailDTO.getEmailTo()), JSONObject.toJSONString(sendEmailDTO.getEmailCC()), response.status()));
        } else {
            log.warn(String.format("发送邮件失败!接收人列表emailTo=%s,抄送人列表emailCC=%s,sendEmailDTO=[%s],返回结果status=[%s],headers=[%s],body=[%s],String=[%s]", JSONObject.toJSONString(sendEmailDTO.getEmailTo()), JSONObject.toJSONString(sendEmailDTO.getEmailCC()), JSONObject.toJSONString(sendEmailDTO), response.status(), JSONObject.toJSONString(response.headers()), JSONObject.toJSONString(response.body()), JSONObject.toJSONString(response.toString())));
        }
        return Boolean.TRUE;
    }

    @Override
    public boolean sendDDRobot(SendRobotDTO sendRobotDTO) {
        if (StringUtils.isBlank(AppConfig.OPEN_API_URL) || sendRobotDTO == null || StringUtils.isBlank(sendRobotDTO.getToken()) || (StringUtils.isBlank(sendRobotDTO.getText()) && StringUtils.isBlank(sendRobotDTO.getMarkdown()))) {
            log.warn("接口开放平台(OpenApi)接口处于关闭状态或消息为空，将无法发送机器人群消息!sendRobotDTO={}", JSONObject.toJSONString(sendRobotDTO));
            return Boolean.FALSE;
        }
        Boolean success = Boolean.FALSE;
        try {
            JSONObject result = openApiRemoteService.sendDDRobot(sendRobotDTO, AppConfig.OPEN_API_ACCOUNT, AppConfig.OPEN_API_PASSWORD, sendRobotDTO.getToken());
            success = result.getBoolean("success");
            if (success) {
                log.info(String.format("发送机器人群消息成功!access_token=[%s],返回结果=[%s]", sendRobotDTO.getToken(), result.getString("message")));
            } else {
                log.warn(String.format("发送机器人群消息失败!access_token=[%s],sendRobotDTO=[%s],返回结果=[%s]", sendRobotDTO.getToken(), JSONObject.toJSONString(sendRobotDTO), JSONObject.toJSONString(result)));
            }
        } catch (Exception e) {
            log.error(String.format("发送机器人群消息远程接口调用失败!access_token=[%s],sendRobotDTO=[%s]", sendRobotDTO.getToken(), JSONObject.toJSONString(sendRobotDTO)), e);
        }

        return success;
    }

    @Override
    public boolean sendDDRobot(List<SendRobotDTO> sendRobotDTOList) {
        if (CollectionUtils.isNotEmpty(sendRobotDTOList)) {
            for (SendRobotDTO sendRobotDTO : sendRobotDTOList) {
                this.sendDDRobot(sendRobotDTO);
            }
        }
        return Boolean.TRUE;
    }

    @Override
    public boolean sendXingeV2(List<SendXingeDTO> sendXingeDTOList) {
        if (CollectionUtils.isNotEmpty(sendXingeDTOList)) {
            for (SendXingeDTO sendXingeDTO : sendXingeDTOList) {
                this.sendXingeV2(sendXingeDTO);
            }
        }
        return Boolean.TRUE;
    }

    @Override
    public boolean sendEmail(List<SendEmailDTO> sendEmailDTOList) {
        if (CollectionUtils.isNotEmpty(sendEmailDTOList)) {
            for (SendEmailDTO sendEmailDTO : sendEmailDTOList) {

                // TODO 测试
                sendEmailDTO.setEmailTo(Arrays.asList("8257"));
                sendEmailDTO.setEmailCC(null);

                this.sendEmail(sendEmailDTO);
            }
        }
        return Boolean.TRUE;
    }

    @Override
    public SendXingeDTO generateSendXingeMessage(String sendXingeMessageTemplate, TemplateData templateData) {
        if (templateData == null) {
            return null;
        }
        if (StringUtils.isBlank(sendXingeMessageTemplate)) {
            sendXingeMessageTemplate = MessageConstant.SEND_XINGE_MONITOR_MESSAGE_TEMPLATE;
        }
        // 根据模板文件和模板数据生成消息字符串
        String bodyNotBase64 = freemarkerService.generateStringByTemplateFile(sendXingeMessageTemplate, templateData);
        if (StringUtils.isBlank(bodyNotBase64)) {
            log.warn("根据模板文件和模板数据生成消息字符串为空!发送信鸽消息模板=[{}],sendXingeMessageTemplateData=[{}]", sendXingeMessageTemplate, JSONObject.toJSONString(templateData));
        }
        SendXingeDTO sendXingeDTO = new SendXingeDTO();
        sendXingeDTO.setRecType(1);
        sendXingeDTO.setSubject("JJ Neural 告警");
        sendXingeDTO.setBodyNotBase64(bodyNotBase64);
        // 合并接收人和抄送人
        List<String> receivers = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(templateData.getAlarmDetailDTO().getReceiveUsers())) {
            CollectionUtils.addAll(receivers, templateData.getAlarmDetailDTO().getReceiveUsers());
        }
        if (CollectionUtils.isNotEmpty(templateData.getAlarmDetailDTO().getCcUsers())) {
            CollectionUtils.addAll(receivers, templateData.getAlarmDetailDTO().getCcUsers());
        }
        sendXingeDTO.setReceivers(receivers);
        return sendXingeDTO;
    }

    @Override
    public SendEmailDTO generateSendEmailMessage(String sendEmailMessageTemplate, TemplateData templateData) {
        if (templateData == null) {
            return null;
        }
        if (StringUtils.isBlank(sendEmailMessageTemplate)) {
            sendEmailMessageTemplate = MessageConstant.SEND_EMAIL_MONITOR_MESSAGE_TEMPLATE;
        }
        // 根据模板文件和模板数据生成消息字符串
        String bodyNotBase64 = freemarkerService.generateStringByTemplateFile(sendEmailMessageTemplate, templateData);
        if (StringUtils.isBlank(bodyNotBase64)) {
            log.warn("根据模板文件和模板数据生成消息字符串为空!发送邮件消息模板=[{}],sendXingeMessageTemplateData=[{}]", sendEmailMessageTemplate, JSONObject.toJSONString(templateData));
        }
        SendEmailDTO sendEmailDTO = new SendEmailDTO();
        // 邮件内容是否是 html 格式，0-普通文本，1-html
        sendEmailDTO.setIsBodyHtml(1);
        // 接收者类型，0-接收人/抄送人列表中是邮件地址，1-接收人/抄送人列表中是用户ID
        sendEmailDTO.setRecType(1);
        sendEmailDTO.setSubject("JJ Neural 告警");
        // TODO 覆盖所有场地：补充数据
        if (BooleanUtils.toBooleanDefaultIfNull(templateData.getIsAllMpid(), Boolean.FALSE)) {
            sendEmailDTO.setSubject(sendEmailDTO.getSubject() + "(覆盖所有场地)");
        }
        sendEmailDTO.setBodyNotBase64(bodyNotBase64);
        sendEmailDTO.setEmailTo(templateData.getAlarmDetailDTO().getReceiveUsers());
        if (CollectionUtils.isNotEmpty(templateData.getAlarmDetailDTO().getCcUsers())) {
            sendEmailDTO.setEmailCC(templateData.getAlarmDetailDTO().getCcUsers());
        }
        return sendEmailDTO;
    }

    @Override
    public SendEmailDTO generateSendMergedEmailMessage(String sendEmailMessageTemplate, MergedTemplateDataDTO mergedTemplateDataDTO) {
        if (mergedTemplateDataDTO == null) {
            return null;
        }
        if (StringUtils.isBlank(sendEmailMessageTemplate)) {
            sendEmailMessageTemplate = MessageConstant.SEND_EMAIL_MONITOR_MERGED_MESSAGE_TEMPLATE;
        }
        // 根据模板文件和模板数据生成消息字符串
        String bodyNotBase64 = freemarkerService.generateStringByTemplateFile(sendEmailMessageTemplate, mergedTemplateDataDTO);
        if (StringUtils.isBlank(bodyNotBase64)) {
            log.warn("根据模板文件和模板数据生成消息字符串为空!发送邮件消息模板=[{}],sendXingeMessageTemplateData=[{}]", sendEmailMessageTemplate, JSONObject.toJSONString(mergedTemplateDataDTO));
        }
        SendEmailDTO sendEmailDTO = new SendEmailDTO();
        // 邮件内容是否是 html 格式，0-普通文本，1-html
        sendEmailDTO.setIsBodyHtml(1);
        // 接收者类型，0-接收人/抄送人列表中是邮件地址，1-接收人/抄送人列表中是用户ID
        sendEmailDTO.setRecType(1);
        sendEmailDTO.setSubject("JJ Neural 告警");
        // TODO 覆盖所有场地：补充数据
//        if (BooleanUtils.toBooleanDefaultIfNull(mergedTemplateDataDTO.getIsAllMpid(), Boolean.FALSE)) {
//            sendEmailDTO.setSubject(sendEmailDTO.getSubject() + "(覆盖所有场地)");
//        }
        sendEmailDTO.setBodyNotBase64(bodyNotBase64);
        sendEmailDTO.setEmailTo(mergedTemplateDataDTO.getEmailTo());
        if (CollectionUtils.isNotEmpty(mergedTemplateDataDTO.getEmailCc())) {
            sendEmailDTO.setEmailCC(mergedTemplateDataDTO.getEmailCc());
        }
        return sendEmailDTO;
    }

    @Override
    public SendRobotDTO generateSendDDRobotMessage(String sendDDRobotMessageTemplate, TemplateData templateData) {
        if (templateData == null) {
            return null;
        }
        if (StringUtils.isBlank(sendDDRobotMessageTemplate)) {
            sendDDRobotMessageTemplate = MessageConstant.SEND_ROBOT_MONITOR_MESSAGE_TEMPLATE;
        }
        // 根据模板文件和模板数据生成消息字符串
        String bodyNotBase64 = freemarkerService.generateStringByTemplateFile(sendDDRobotMessageTemplate, templateData);
        if (StringUtils.isBlank(bodyNotBase64)) {
            log.warn("根据模板文件和模板数据生成消息字符串为空!发送机器人群消息模板=[{}],sendXingeMessageTemplateData=[{}]", sendDDRobotMessageTemplate, JSONObject.toJSONString(templateData));
        }
        Map<String, String> text = new HashMap<>();
        text.put("content", bodyNotBase64);

        SendRobotDTO sendRobotDTO = new SendRobotDTO();
        sendRobotDTO.setToken(StringUtils.defaultIfBlank(templateData.getAlarmDetailDTO().getRobotToken(), AppConfig.ROBOT_MESSAGE_TOKEN_OF_DEFAULT));
        sendRobotDTO.setMsgtype("text");
        sendRobotDTO.setText(JSONObject.toJSONString(text));

        return sendRobotDTO;
    }

    @Override
    public SendRobotDTO generateSendDDRobotMdMessage(String sendDDRobotMessageMdTemplate, TemplateData templateData) {
        if (templateData == null) {
            return null;
        }
        if (StringUtils.isBlank(sendDDRobotMessageMdTemplate)) {
            sendDDRobotMessageMdTemplate = MessageConstant.SEND_ROBOT_MONITOR_MESSAGE_MD_TEMPLATE;
        }
        // 根据模板文件和模板数据生成消息字符串
        String bodyNotBase64 = freemarkerService.generateStringByTemplateFile(sendDDRobotMessageMdTemplate, templateData);
        if (StringUtils.isBlank(bodyNotBase64)) {
            log.warn("根据模板文件和模板数据生成消息字符串为空!发送机器人群消息模板=[{}],sendXingeMessageTemplateData=[{}]", sendDDRobotMessageMdTemplate, JSONObject.toJSONString(templateData));
        }
        Map<String, String> text = new HashMap<>();
        text.put("text", bodyNotBase64);
        text.put("title", "JJ Neural 安全风控告警");

        SendRobotDTO sendRobotDTO = new SendRobotDTO();
        sendRobotDTO.setToken(StringUtils.defaultIfBlank(templateData.getAlarmDetailDTO().getRobotToken(), AppConfig.ROBOT_MESSAGE_TOKEN_OF_DEFAULT));
        sendRobotDTO.setMsgtype("markdown");
        sendRobotDTO.setMarkdown(JSONObject.toJSONString(text));

        return sendRobotDTO;
    }

    @Override
    public boolean sendXingeAdministratorMessage(String msg, List<String> receivers) {
        List<String> receiversAll = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(receivers)) {
            CollectionUtils.addAll(receiversAll, receivers);
        }
        if (CollectionUtils.isNotEmpty(AppConfig.SYSTEM_ADMINISTRATOR_USERIDS)) {
            CollectionUtils.addAll(receiversAll, AppConfig.SYSTEM_ADMINISTRATOR_USERIDS);
        }
        SendXingeDTO sendXingeDTO = new SendXingeDTO();
        sendXingeDTO.setBodyNotBase64("JJ Neural 系统消息：\r\n尊敬的用户，您好，【" + msg + "】，请留意!\r\n此消息发送时间：" + DateUtil.formatDateTime(new Date()));
        sendXingeDTO.setReceivers(receiversAll);
        sendXingeDTO.setSubject("JJ Neural 告警");
        sendXingeDTO.setRecType(1);
        this.sendXingeV2(sendXingeDTO);
        return Boolean.TRUE;
    }
}
