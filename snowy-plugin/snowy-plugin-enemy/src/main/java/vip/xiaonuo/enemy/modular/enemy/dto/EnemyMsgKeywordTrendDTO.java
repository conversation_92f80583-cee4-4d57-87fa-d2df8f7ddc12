/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.enemy.modular.enemy.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

/**
 * 关键词趋势统计数据传输对象
 *
 * <AUTHOR>
 * @date 2025/05/15 16:30
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "关键词趋势统计数据传输对象")
public class EnemyMsgKeywordTrendDTO implements Serializable {

    private static final Long serialVersionUID = 1L;

    /**
     * 日期
     */
    @ApiModelProperty(value = "日期", position = 1)
    private String day;

    /**
     * 关键词
     */
    @ApiModelProperty(value = "关键词", position = 2)
    private String hitKeyword;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量", position = 3)
    private Long count;
}
