<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=yes">
    <title><PERSON><PERSON> 告警</title>
    <style>
        html {
            font-size: 16px;
        }

        body {
            font-family: "Open Sans", "Clear Sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
            font-size: 1rem;
            line-height: 1.6;
            color: #333333;
            margin: 0;
            padding: 0;
        }

        .toc {
            min-height: 3.58rem;
            position: relative;
            font-size: 0.9rem;
            border-radius: 10px;
            background: #fafafa;
            padding: 15px;
            margin: 20px 28px;
        }

        .toc-content {
            position: relative;
            margin-left: 0;
        }

        .toc-title {
            font-size: 1rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .toc-list {
            list-style-type: none;
            padding: 0;
            margin: 0;
        }

        .toc-item {
            margin: 0;
            position: relative;
        }

        .toc-link {
            display: inline-block;
            cursor: pointer;
            color: #4167b2;
            text-decoration: none;
            font-size: 0.9rem;
            line-height: 1.6;
        }

        .toc-link:hover {
            text-decoration: underline;
        }

        .toc-h1 .toc-link {
            margin-left: 0;
            font-weight: bold;
        }

        .toc-h2 .toc-link {
            margin-left: 2em;
        }

        .toc-h3 .toc-link {
            margin-left: 4em;
        }

        .toc-h4 .toc-link {
            margin-left: 6em;
        }

        .toc-h5 .toc-link {
            margin-left: 8em;
        }

        .toc-h6 .toc-link {
            margin-left: 10em;
        }

        @media screen and (max-width: 48em) {
            .toc-h3 .toc-link { margin-left: 3.5em; }
            .toc-h4 .toc-link { margin-left: 5em; }
            .toc-h5 .toc-link { margin-left: 6.5em; }
            .toc-h6 .toc-link { margin-left: 8em; }
        }

        .game-title {
            background: #f5f5f5;
            border-left: 4px solid #ED6D00;
            padding: 12px 20px 12px 15px;
            margin: 20px 28px 10px 28px;
            font-size: 1.2rem;
            font-weight: bold;
            color: #333;
        }

        h1, h2, h3, h4, h5, h6 {
            margin-top: 1rem;
            margin-bottom: 1rem;
            font-weight: bold;
            line-height: 1.4;
        }

        h1 { font-size: 2.25rem; }
        h2 { font-size: 1.75rem; }
        h3 { font-size: 1.5rem; }
        h4 { font-size: 1.25rem; }
        h5 { font-size: 1rem; }
        h6 { font-size: 1rem; color: #777; }

        p, blockquote, ul, ol, dl {
            margin: 0.8em 0;
        }

        a {
            color: #4167b2;
            text-decoration: none;
        }

        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
<div><br></div>
<div>
        <div><br></div>
        <style type="text/css">
body{ background:#ffffff; margin:0 auto; padding:0; text-align:left; font-size:12px; font-family: "微软雅黑","宋体";}
table{ font-size:12px; font-family: "微软雅黑","宋体";}
.table_c{border:1px solid #ccc;border-collapse:collapse; }
.table_c td{border:1px solid #ccc; border-collapse:collapse;}
.table_b{border:1px solid #666;border-collapse:collapse; }
.table_b td{ border-collapse:collapse; border:1px solid #ccc;}
.table_b th{color:#fff; background:#666;}
a:link{ color:#3366cc; font-weight:normal; }
a:visited { color: #3366cc;}
a:hover{ color:#000; }
a:active { color:#3366cc; }
td{ line-height:20px;}
        </style>
        <style>
        .three-banner a{
          display:block;
          float:left;
          border:1px solid #e5e5e5;
          position:relative;
          margin-left:18px;
        }
        .three-banner a:first-child{margin-left:0;}
        .cover{
          background: #e6e6e6;
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          opacity: 0;
        }
        .three-banner a:hover .cover{
          opacity: 0.3;
        }
.ant-tag-has-color {
    border-color: transparent;
	color: #fff;
}

.ant-tag {
    box-sizing: border-box;
    margin: 3px 4px 3px 0;
    color: rgba(0, 0, 0, .85);
    font-variant: tabular-nums;
    line-height: 1.5715;
    list-style: none;
    font-feature-settings: "tnum";
    display: inline-block;
    height: auto;
    padding: 0 7px;
    font-size: 12px;
    line-height: 20px;
    white-space: nowrap;
    background: #FCE4D6;
    border: 1px solid #FCE4D6;
    border-radius: 2px;
    opacity: 1;
    transition: all .3s;
}
        </style>

        <table width="1250" border="0" cellspacing="0" align="center" cellpadding="0" style="border: #ccc 1px solid;">
            <tbody>
            <tr>
                <td>
                    <table width="1250" border="0" cellpadding="0" cellspacing="0" bgcolor="fbfbfb"
                           style="border-bottom: #eeeeee 1px solid;border-top: #cc0000 1px solid;">

                        <tbody>
                       <tr>
                            <td width="130" height="75">
                                <img src="data:image/png;base64,iVBORw0KGgo"
                                     width="130" height="41" style="padding-left:5px;"></td>
                                     <td width="1120" align="center" style="font-size: 25px; font-weight: bold; color: #999999;padding-right: 130px;">
									 <strong><font color="#ED6D00">安全风控告警(JJ Neural)<#if isAllMpid?? && isAllMpid>(覆盖所有场地)</#if></font></strong></td>
                        </tr>
                        </tbody>
                    </table>

<#if templateDataList?? && templateDataList?size gt 0>
                    <div class="toc">
                        <div class="toc-title">目录</div>
                        <ul class="toc-list">
                        <#list templateDataList as templateData>
                            <li class="toc-item">
                                <a class="toc-link" href="#game-${templateData.alarmDetailDTO.mpId?c}">
                                    ${templateData.alarmDetailDTO.mpId?c}-${(templateData.alarmDetailDTO.dimProductInfo.gameName)!"未知游戏名"}-${(templateData.alarmDetailDTO.dimProductInfo.mpName)!"未知比赛产品名"}
                                </a>
                            </li>
                        </#list>
                        </ul>
                    </div>

<#list templateDataList as templateData>
                    <div class="game-title">
                        <a name="game-${templateData.alarmDetailDTO.mpId?c}"></a>
                        ${templateData.alarmDetailDTO.mpId?c}-${(templateData.alarmDetailDTO.dimProductInfo.gameName)!"未知游戏名"}-${(templateData.alarmDetailDTO.dimProductInfo.mpName)!"未知比赛产品名"}
                    </div>
                    <table width="1200" border="0" align="center" cellspacing="0" cellpadding="0"
                           style="margin: 0 28px 10px 28px;">
                        <tbody>
                        <tr>
                            <td style="font-size: 14px; color: #666666; padding-bottom: 6px;">
                            <#if isAllMpid?? && isAllMpid><strong>注意：该游戏未设置告警接收人，可点击进入
                            <a target="_blank" href="http://192.168.9.139/snowy/#/biz/alarm/alarmNotifyIndex?gameId=${templateData.alarmDetailDTO.dimProductInfo.gameId?c}"><u>[${templateData.alarmDetailDTO.dimProductInfo.gameId?c}-${(templateData.alarmDetailDTO.dimProductInfo.gameName)!"未知游戏名"}]</u></a> 设置 ( 详情请登录【JJ Neural-->业务-->监控告警-->告警通知信息】查看 )</strong><br><br>
                            </#if>
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;比赛 <a target="_blank" href="http://192.168.9.139/webroot/decision/v5/design/report/90f59261f1154bf492cda3d5e22e9217/view?mpId=${templateData.alarmDetailDTO.mpId?c}"><u>[${templateData.alarmDetailDTO.mpId?c}-${(templateData.alarmDetailDTO.dimProductInfo.gameName)!"未知游戏名"}-${(templateData.alarmDetailDTO.dimProductInfo.mpName)!"未知比赛产品名"}]</u></a> 于 ${templateData.timeStart} 至 ${templateData.timeEnd} 期间，触发告警：<br>

<#list templateData.triggerAlarmResultDTOList as triggerAlarmResult>
<#if triggerAlarmResult.forceAlarm>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<strong>${triggerAlarmResult_index+1}.</strong>   <strong>${triggerAlarmResult.conditionDetailDTO.conditionName}</strong> 用户数达到 <strong>${triggerAlarmResult.monitorValueAbsolute}</strong> 人 ；<br>
<#else>
<#if triggerAlarmResult.conditionDetailDTO.valueType=='1'>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<strong>${triggerAlarmResult_index+1}.</strong>   <strong>${triggerAlarmResult.conditionDetailDTO.conditionName}</strong> 用户数达到 <strong>${triggerAlarmResult.monitorValueAbsolute}</strong> 人 ；<br>
<#elseif triggerAlarmResult.conditionDetailDTO.valueType=='2'>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<strong>${triggerAlarmResult_index+1}.</strong>   <strong>${triggerAlarmResult.conditionDetailDTO.conditionName}</strong> 用户数达到 <strong>${triggerAlarmResult.monitorValueAbsolute}</strong> 人 ；<br>
<#elseif triggerAlarmResult.conditionDetailDTO.valueType=='3'>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<strong>${triggerAlarmResult_index+1}.</strong>   <strong>${triggerAlarmResult.conditionDetailDTO.conditionName}</strong> 用户数达到 <strong>${triggerAlarmResult.monitorValueAbsolute}</strong> 人 ；<br>
<#elseif triggerAlarmResult.conditionDetailDTO.valueType=='4'>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<strong>${triggerAlarmResult_index+1}.</strong>   <strong>${triggerAlarmResult.conditionDetailDTO.conditionName}</strong> 用户数达到 <strong>${triggerAlarmResult.monitorValueAbsolute}</strong> 人 ( 相对于历史值的变化率为 ${triggerAlarmResult.monitorValueRelative}%，告警阈值<#if (triggerAlarmResult.conditionDetailDTO.geValue)??> ${triggerAlarmResult.conditionDetailDTO.geValue}%<#else> 低于${triggerAlarmResult.conditionDetailDTO.leValue}%</#if> )；<br>
<#elseif triggerAlarmResult.conditionDetailDTO.valueType=='5'>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<strong>${triggerAlarmResult_index+1}.</strong>   <strong>${triggerAlarmResult.conditionDetailDTO.conditionName}</strong> 用户数达到 <strong>${triggerAlarmResult.monitorValueAbsolute}</strong> 人 ( 相对于历史值的变化率为 ${triggerAlarmResult.monitorValueRelative}%，告警阈值<#if (triggerAlarmResult.conditionDetailDTO.geValue)??> ${triggerAlarmResult.conditionDetailDTO.geValue}%<#else> 低于${triggerAlarmResult.conditionDetailDTO.leValue}%</#if> )；<br>
<#elseif triggerAlarmResult.conditionDetailDTO.valueType=='6'>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<strong>${triggerAlarmResult_index+1}.</strong>   <strong>${triggerAlarmResult.conditionDetailDTO.conditionName}</strong> 用户数达到 <strong>${triggerAlarmResult.monitorValueAbsolute}</strong> 人 ( 相对于历史值的变化率为 ${triggerAlarmResult.monitorValueRelative}%，告警阈值<#if (triggerAlarmResult.conditionDetailDTO.geValue)??> ${triggerAlarmResult.conditionDetailDTO.geValue}%<#else> 低于${triggerAlarmResult.conditionDetailDTO.leValue}%</#if> )；<br>
<#elseif triggerAlarmResult.conditionDetailDTO.valueType=='7'>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<strong>${triggerAlarmResult_index+1}.</strong>   <strong>${triggerAlarmResult.conditionDetailDTO.conditionName}</strong> 用户数达到 <strong>${triggerAlarmResult.monitorValueAbsolute}</strong> 人 ( 相对于历史值的变化率为 ${triggerAlarmResult.monitorValueRelative}%，告警阈值<#if (triggerAlarmResult.conditionDetailDTO.geValue)??> ${triggerAlarmResult.conditionDetailDTO.geValue}%<#else> 低于${triggerAlarmResult.conditionDetailDTO.leValue}%</#if> )；<br>
<#elseif triggerAlarmResult.conditionDetailDTO.valueType=='8'>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<strong>${triggerAlarmResult_index+1}.</strong>   <strong>${triggerAlarmResult.conditionDetailDTO.conditionName}</strong> 用户数达到 <strong>${triggerAlarmResult.monitorValueAbsolute}</strong> 人 ( 相对于历史值的变化率为 ${triggerAlarmResult.monitorValueRelative}%，告警阈值<#if (triggerAlarmResult.conditionDetailDTO.geValue)??> ${triggerAlarmResult.conditionDetailDTO.geValue}%<#else> 低于${triggerAlarmResult.conditionDetailDTO.leValue}%</#if> )；<br>
<#elseif triggerAlarmResult.conditionDetailDTO.valueType=='9'>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<strong>${triggerAlarmResult_index+1}.</strong>   <strong>${triggerAlarmResult.conditionDetailDTO.conditionName}</strong> 用户数达到 <strong>${triggerAlarmResult.monitorValueAbsolute}</strong> 人 ( 相对于历史值的变化率为 ${triggerAlarmResult.monitorValueRelative}%，告警阈值<#if (triggerAlarmResult.conditionDetailDTO.geValue)??> ${triggerAlarmResult.conditionDetailDTO.geValue}%<#else> 低于${triggerAlarmResult.conditionDetailDTO.leValue}%</#if> )；<br>
</#if>
</#if>
</#list>
					<#if templateData.sampleUserDTOList?? && (templateData.sampleUserDTOList?size > 0)>
					<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<strong>异常用户ID样本</strong>（只展示当日对局数 Top5 的异常用户）：<br>
<#list templateData.sampleUserDTOList as sampleUserDTO>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;${(sampleUserDTO.conditionName)!""}：${(sampleUserDTO.userIds)!""}<br>
</#list>
					<#else >
					</#if>

					<#if templateData.imageFileNameDTOList??>
					<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<strong>触发变化趋势图：</strong><br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;展示最近7天对应告警规则当日触发的异常用户数、异常用户的损益以及异常用户的对局数变化趋势。<br><br>
					<#list templateData.imageFileNameDTOList as imageFileNameDTO>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<img src="data:image/png;base64,${(imageFileNameDTO.base64)!""}"><br>
					</#list>
					<#else >
					</#if>

                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <table width="1200" border="0" cellspacing="0" cellpadding="0" align="center">
                        <tbody>
                        <tr>
                            <td style="font-size: 14px; color: 333333; font-weight: bold; padding-bottom:15px;">
                                <br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;[${templateData.alarmDetailDTO.mpId?c}-${(templateData.alarmDetailDTO.dimProductInfo.gameName)!"未知游戏名"}-${(templateData.alarmDetailDTO.dimProductInfo.mpName)!"未知比赛产品名"}]最近7日异常用户参赛详细信息如下：
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <table width="1200" border="0" align="center" class="table_c"
                                       style="border-collapse:collapse;">
                                    <tbody>
                                    <tr bgcolor="#F0F8FF">
                                        <td height="28" align="center" style="border:1px solid #ccc;">
                                            日期
                                        </td>
                                        <!--
                                        <td height="28" align="center" style="border:1px solid #ccc;">
                                            比赛产品ID
                                        </td>
                                        -->
										<td height="28" align="center" style="border:1px solid #ccc;">
                                            触发规则
                                        </td>
                                        <td height="28" align="center" style="border:1px solid #ccc;">
                                            触发人数
                                        </td>
                                        <td height="28" align="center" style="border:1px solid #ccc;">
                                            比赛次数
                                        </td>
                                        <td height="28" align="center" style="border:1px solid #ccc;">
                                            比赛时长(秒)
                                        </td>
										<td height="28" align="center" style="border:1px solid #ccc;">
                                            平均比赛时长(秒)
                                        </td>
                                        <td height="28" align="center" style="border:1px solid #ccc;">
                                            金币赢取
                                        </td>
                                        <td height="28" align="center" style="border:1px solid #ccc;">
                                            金币消费
                                        </td>
                                        <td height="28" align="center" style="border:1px solid #ccc;">
                                            损益
                                        </td>
                                        <td height="28" align="center" style="border:1px solid #ccc;">
                                            冠军次数
                                        </td>
                                        <td height="28" align="center" style="border:1px solid #ccc;">
                                            总经验值
                                        </td>
                                        <td height="28" align="center" style="border:1px solid #ccc;">
                                            总大师分
                                        </td>
                                        <td height="28" align="center" style="border:1px solid #ccc;">
                                            对局数
                                        </td>
                                        <td height="28" align="center" style="border:1px solid #ccc;">
                                            平均对局数(次/人)
                                        </td>
										<td height="28" align="center" style="border:1px solid #ccc;">
                                            触发告警
                                        </td>
                                    </tr>
                                    <#list templateData.dayAndMonitorGameDayMap?keys as key>
                                    <#list templateData.dayAndMonitorGameDayMap[key] as monitorGameDay>
									<#if ((monitorGameDay.alarm)!'0')=='1'>
                                    <tr bgcolor="#FCE4D6">
									<#else >
									<tr bgcolor="#FFFFFF">
									</#if>
									    <#if monitorGameDay_index==0>
										<td rowspan="${templateData.dayAndMonitorGameDayMap[key]?size}" bgcolor="#FFFFFF" height="28" align="center" style="border:1px solid #ccc;">
                                            ${(monitorGameDay.day)!""}
                                        </td>
                                        </#if>
                                        <!--
                                        <td height="28" align="center" style="border:1px solid #ccc;">
                                            ${((monitorGameDay.mpId)?c)!""}
                                        </td>
                                        -->
										<td height="28" align="center" style="border:1px solid #ccc;">
                                            ${(monitorGameDay.conditionName)!""}
                                        </td>
                                        <td height="28" align="center" style="border:1px solid #ccc;">
                                            ${(monitorGameDay.uidCnt)!"0"}
                                        </td>
                                        <td height="28" align="center" style="border:1px solid #ccc;">
                                            ${(monitorGameDay.matchCount)!"0"}
                                        </td>
                                        <td height="28" align="center" style="border:1px solid #ccc;">
                                            ${(monitorGameDay.matchTime)!"0"}
                                        </td>
										<td height="28" align="center" style="border:1px solid #ccc;">
										<#if (monitorGameDay.matchCount)??>
										<#if monitorGameDay.matchCount==0>
                                            0
                                        <#else >
                                        ${((monitorGameDay.matchTime/monitorGameDay.matchCount)?round)!"0"}
                                        </#if>
                                        <#else >
                                        0
                                        </#if>
                                        </td>
                                        <td height="28" align="center" style="border:1px solid #ccc;">
                                            ${(monitorGameDay.coinIncome)!"0"}
                                        </td>
                                        <td height="28" align="center" style="border:1px solid #ccc;">
                                            ${(monitorGameDay.coinPay)!"0"}
                                        </td>
                                        <td height="28" align="center" style="border:1px solid #ccc;">
                                            ${(monitorGameDay.income)!"0"}
                                        </td>
                                        <td height="28" align="center" style="border:1px solid #ccc;">
                                            ${(monitorGameDay.championCnt)!"0"}
                                        </td>
                                        <td height="28" align="center" style="border:1px solid #ccc;">
                                            ${(monitorGameDay.score)!"0"}
                                        </td>
                                        <td height="28" align="center" style="border:1px solid #ccc;">
                                            ${(monitorGameDay.masterScore)!"0"}
                                        </td>
                                        <td height="28" align="center" style="border:1px solid #ccc;">
                                            ${(monitorGameDay.roundCnt)!"0"}
                                        </td>
                                        <td height="28" align="center" style="border:1px solid #ccc;">
                                        <#if (monitorGameDay.uidCnt)??>
										<#if monitorGameDay.uidCnt==0>
                                            0
                                        <#else >
                                        ${((monitorGameDay.roundCnt/monitorGameDay.uidCnt)?round)!"0"}
                                        </#if>
                                        <#else >
                                        0
                                        </#if>
                                        </td>
										<td height="28" align="center" style="border:1px solid #ccc;">
										<#if ((monitorGameDay.alarm)!'0')=='1'>
										是
										<#else >
										否
										</#if>
                                        </td>
                                    </tr>
                                    </#list>
                                    </#list>
                                    </tbody>
                                </table>
					<#if templateData.sampleUserDTOList?? && (templateData.sampleUserDTOList?size > 0)>
                                <br>
                    <table width="1200" border="0" cellspacing="0" cellpadding="0" align="center">
                        <tbody>
                        <tr>
                            <td style="font-size: 14px; color: 333333; font-weight: bold; padding-bottom:15px;">
                                <br>异常用户ID样本及风控标签如下：
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <table width="1200" border="0" align="center" class="table_c"
                                       style="border-collapse:collapse; table-layout: fixed;">
                                    <colgroup>
                                        <col style="width: 9%;">
                                        <col style="width: 9%;">
                                        <col style="width: 73%;">
                                        <col style="width: 9%;">
                                    </colgroup>
                                    <tbody>
                                    <tr bgcolor="#F0F8FF">
                                        <td height="28" align="center" style="border:1px solid #ccc;">
                                            触发规则
                                        </td>
                                        <td height="28" align="center" style="border:1px solid #ccc;">
                                            用户ID
                                        </td>
                                        <td height="28" align="center" style="border:1px solid #ccc;">
                                            风控标签
                                        </td>
                                        <td height="28" align="center" style="border:1px solid #ccc;">
                                            规则说明
                                        </td>
                                    </tr>
                                    <#list templateData.sampleUserDTOList as sampleUserDTO>
                                    <#list sampleUserDTO.userIdAndTagsMap?keys as key>
									<tr bgcolor="#FFFFFF">
									    <#if key_index==0>
										<td rowspan="${sampleUserDTO.userIdAndTagsMap?size}" height="28" align="center" style="border:1px solid #ccc;">
                                            <strong>${(sampleUserDTO.conditionName)!""}</strong>
                                        </td>
                                        </#if>
										<td height="28" align="center" style="border:1px solid #ccc;">
                                            ${(key)!""}
                                        </td>
                                        <td height="28" align="center" style="border:1px solid #ccc;">
                                            <#if (sampleUserDTO.userIdAndTagsMap[key])?? && ((sampleUserDTO.userIdAndTagsMap[key])?size > 0)>
                                            <#list (sampleUserDTO.userIdAndTagsMap[key]) as tagZh>
                                            <span class="ant-tag ant-tag-has-color">${(tagZh)!""}</span>
                                            </#list>
                                            <#else >
                                            无
                                            </#if>
                                        </td>
									    <#if key_index==0>
										<td rowspan="${sampleUserDTO.userIdAndTagsMap?size}" height="28" align="center" style="border:1px solid #ccc;">
                                            ${(sampleUserDTO.conditionDetailDTO.description)!"无说明"}
                                        </td>
                                        </#if>
                                    </tr>
                                    </#list>
                                    </#list>
                                    </tbody>
                                </table>
					<#else >
					</#if>
                            </td>
                        </tr>

                        <tr>
                        </tr>
                        </tbody>
                    </table>

                    <table width="1200" border="0" align="center" cellspacing="0" cellpadding="0"
                           style="margin: 34px 28px 16px 28px; border-top: #e4e4e4 1px solid;">
                        <tbody>
                        </tbody>
                    </table>
                </td>
            </tr>
            </tbody>
        </table>

</#list>
</#if>

                            <table width="1200" border="0" cellspacing="0" cellpadding="0" align="center">
                                <tbody>
                        <tr>
                            <td style="font-size: 13px; line-height: 20px; color: 333333; padding-top:15px; ">
                            此告警基于<strong>安全风控平台客户端相关数据</strong>，若需对此批风险用户从更多维度综合分析，请联系安全风控平台<strong>常俊杰</strong>或<strong>李杰</strong>，若对客户端安全相关数据有疑问，请联系客户端安全<strong>陈健</strong>或<strong>杜威</strong>。<br>
							<strong>下一步，您还可以：</strong><br>
							1.访问<a target="_blank" href="http://192.168.9.139/webroot/decision/v5/design/report/90f59261f1154bf492cda3d5e22e9217/view">【游戏触发监控看板】</a>，查看该比赛所有标签触发详情。<br>
							2.访问<a target="_blank" href="http://192.168.9.139/webroot/decision/v5/design/report/1c2816174e524c67ad0fe32978636589/view">【游戏触发排行榜看板】</a>，查看各标签游戏触发排行榜。<br>
							3.访问<a target="_blank" href="http://192.168.9.139/webroot/decision/v5/design/report/3675bbfd034841888720d02a8735f74d/view">【游戏监控值与触发阈值看板】</a>，查看各游戏每日监控值与触发阈值详情。<br>
							4.访问<a target="_blank" href="http://192.168.9.139/webroot/decision/v5/design/report/6d193f057d9d4496b2948d6587b01436/view">【游戏告警规则及日志查询看板】</a>，查看告警规则及日志详情。<br>
							（首次登录系统请联系【技术支撑中心-信息安全组-<strong>陈健</strong>或<strong>杜威</strong>】申请账号和开通权限）<br><br>
                            此消息发送时间：${alarmTime}<br>
                            </td>
                        </tr>
                                <tr>
                                </tr>
                                </tbody>
                            </table>

            </td>
        </tr>
        </tbody>
    </table>
</div>
</body>
</html>
